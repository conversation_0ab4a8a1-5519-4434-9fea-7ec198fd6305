#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=42696, tid=32832
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Wed Dec 11 16:14:44 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.516137 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000018efd087fd0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=32832, stack(0x0000001c5ef00000,0x0000001c5f000000) (1024K)]


Current CompileTask:
C2:    516 1084       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x0000001c5ef00000,0x0000001c5f000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0xc613d]
V  [jvm.dll+0xc6673]
V  [jvm.dll+0x81c558]
V  [jvm.dll+0x60672d]
V  [jvm.dll+0x60444b]
V  [jvm.dll+0x6040ad]
V  [jvm.dll+0x603f90]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60410e]
V  [jvm.dll+0x60b49c]
V  [jvm.dll+0x258b52]
V  [jvm.dll+0x258f0f]
V  [jvm.dll+0x2517e5]
V  [jvm.dll+0x24f03e]
V  [jvm.dll+0x1cd074]
V  [jvm.dll+0x25e88c]
V  [jvm.dll+0x25cdd6]
V  [jvm.dll+0x3fdff6]
V  [jvm.dll+0x868868]
V  [jvm.dll+0x6e1edd]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000018e9b38bcf0, length=16, elements={
0x0000018ef32d3690, 0x0000018efd010180, 0x0000018efd011d50, 0x0000018efd06b9d0,
0x0000018efd0855e0, 0x0000018efd086040, 0x0000018efd086aa0, 0x0000018efd087fd0,
0x0000018efd09cff0, 0x0000018efd22e060, 0x0000018efd22eae0, 0x0000018e9b3416b0,
0x0000018e9b3a6be0, 0x0000018e9b41f6a0, 0x0000018e9b526990, 0x0000018e9b5c72c0
}

Java Threads: ( => current thread )
  0x0000018ef32d3690 JavaThread "main"                              [_thread_blocked, id=36420, stack(0x0000001c5e100000,0x0000001c5e200000) (1024K)]
  0x0000018efd010180 JavaThread "Reference Handler"          daemon [_thread_blocked, id=37232, stack(0x0000001c5e900000,0x0000001c5ea00000) (1024K)]
  0x0000018efd011d50 JavaThread "Finalizer"                  daemon [_thread_blocked, id=13280, stack(0x0000001c5ea00000,0x0000001c5eb00000) (1024K)]
  0x0000018efd06b9d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=21100, stack(0x0000001c5eb00000,0x0000001c5ec00000) (1024K)]
  0x0000018efd0855e0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=2288, stack(0x0000001c5ec00000,0x0000001c5ed00000) (1024K)]
  0x0000018efd086040 JavaThread "Service Thread"             daemon [_thread_blocked, id=32436, stack(0x0000001c5ed00000,0x0000001c5ee00000) (1024K)]
  0x0000018efd086aa0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=12152, stack(0x0000001c5ee00000,0x0000001c5ef00000) (1024K)]
=>0x0000018efd087fd0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=32832, stack(0x0000001c5ef00000,0x0000001c5f000000) (1024K)]
  0x0000018efd09cff0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=42612, stack(0x0000001c5f000000,0x0000001c5f100000) (1024K)]
  0x0000018efd22e060 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22384, stack(0x0000001c5f100000,0x0000001c5f200000) (1024K)]
  0x0000018efd22eae0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=40012, stack(0x0000001c5f200000,0x0000001c5f300000) (1024K)]
  0x0000018e9b3416b0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=40276, stack(0x0000001c5f300000,0x0000001c5f400000) (1024K)]
  0x0000018e9b3a6be0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_blocked, id=39328, stack(0x0000001c5f500000,0x0000001c5f600000) (1024K)]
  0x0000018e9b41f6a0 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=3432, stack(0x0000001c5f600000,0x0000001c5f700000) (1024K)]
  0x0000018e9b526990 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=40264, stack(0x0000001c5f700000,0x0000001c5f800000) (1024K)]
  0x0000018e9b5c72c0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=28208, stack(0x0000001c5f800000,0x0000001c5f900000) (1024K)]
Total: 16

Other Threads:
  0x0000018efcfe2100 VMThread "VM Thread"                           [id=42576, stack(0x0000001c5e800000,0x0000001c5e900000) (1024K)]
  0x0000018efcfd0bb0 WatcherThread "VM Periodic Task Thread"        [id=43000, stack(0x0000001c5e700000,0x0000001c5e800000) (1024K)]
  0x0000018efa2f0980 WorkerThread "GC Thread#0"                     [id=27272, stack(0x0000001c5e200000,0x0000001c5e300000) (1024K)]
  0x0000018ef334a9d0 ConcurrentGCThread "G1 Main Marker"            [id=13908, stack(0x0000001c5e300000,0x0000001c5e400000) (1024K)]
  0x0000018ef334b680 WorkerThread "G1 Conc#0"                       [id=35536, stack(0x0000001c5e400000,0x0000001c5e500000) (1024K)]
  0x0000018efa375520 ConcurrentGCThread "G1 Refine#0"               [id=44420, stack(0x0000001c5e500000,0x0000001c5e600000) (1024K)]
  0x0000018efa376090 ConcurrentGCThread "G1 Service"                [id=11516, stack(0x0000001c5e600000,0x0000001c5e700000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0      569 1084       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=1
LinkageErrors=35

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 16384K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 0 survivors (0K)
 Metaspace       used 17831K, committed 18112K, reserved 1114112K
  class space    used 1754K, committed 1920K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x000000062287b658, 0x0000000622c00000| 12%| E|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Complete 
| 124|0x0000000622c00000, 0x0000000623000000, 0x0000000623000000|100%| E|CS|TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x0000018ef9230000,0x0000018efa220000] _byte_map_base: 0x0000018ef6212000

Marking Bits: (CMBitMap*) 0x0000018ef333a2e0
 Bits: [0x0000018e8f000000, 0x0000018e96f10000)

Polling page: 0x0000018ef1aa0000

Metaspace:

Usage:
  Non-class:     15.70 MB used.
      Class:      1.71 MB used.
       Both:     17.41 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.81 MB ( 25%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.88 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.69 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  78.00 KB
       Class:  14.17 MB
        Both:  14.25 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 228.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 283.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 416.
num_chunk_merges: 0.
num_chunk_splits: 228.
num_chunks_enlarged: 95.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=367Kb max_used=367Kb free=119632Kb
 bounds [0x0000018e87ad0000, 0x0000018e87d40000, 0x0000018e8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=1875Kb max_used=1875Kb free=118124Kb
 bounds [0x0000018e80000000, 0x0000018e80270000, 0x0000018e87530000]
CodeHeap 'non-nmethods': size=5760Kb used=1438Kb max_used=1463Kb free=4321Kb
 bounds [0x0000018e87530000, 0x0000018e877a0000, 0x0000018e87ad0000]
 total_blobs=1657 nmethods=1137 adapters=425
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.503 Thread 0x0000018efd09cff0 nmethod 1121 0x0000018e801c7590 code [0x0000018e801c7780, 0x0000018e801c7dc0]
Event: 0.503 Thread 0x0000018efd09cff0 1122       3       sun.security.util.ObjectIdentifier::checkOtherComponent (41 bytes)
Event: 0.503 Thread 0x0000018efd09cff0 nmethod 1122 0x0000018e801c8010 code [0x0000018e801c8240, 0x0000018e801c88c8]
Event: 0.503 Thread 0x0000018efd09cff0 1123       3       java.lang.invoke.LambdaFormBuffer::changeName (149 bytes)
Event: 0.504 Thread 0x0000018efd09cff0 nmethod 1123 0x0000018e801c8b90 code [0x0000018e801c8d60, 0x0000018e801c92a8]
Event: 0.507 Thread 0x0000018efd09cff0 1124       3       java.lang.Byte::hashCode (8 bytes)
Event: 0.508 Thread 0x0000018efd09cff0 nmethod 1124 0x0000018e801c9490 code [0x0000018e801c9620, 0x0000018e801c9788]
Event: 0.508 Thread 0x0000018efd09cff0 1126       3       java.lang.invoke.MethodHandleNatives::refKindHasReceiver (33 bytes)
Event: 0.508 Thread 0x0000018efd09cff0 nmethod 1126 0x0000018e801c9810 code [0x0000018e801c99a0, 0x0000018e801c9af8]
Event: 0.508 Thread 0x0000018efd09cff0 1125       3       java.lang.Byte::hashCode (2 bytes)
Event: 0.508 Thread 0x0000018efd09cff0 nmethod 1125 0x0000018e801c9b90 code [0x0000018e801c9d20, 0x0000018e801c9e10]
Event: 0.509 Thread 0x0000018efd09cff0 1127       3       sun.security.util.math.intpoly.P256OrderField::carryReduce (577 bytes)
Event: 0.510 Thread 0x0000018efd09cff0 nmethod 1127 0x0000018e801c9e90 code [0x0000018e801ca040, 0x0000018e801ca598]
Event: 0.510 Thread 0x0000018efd09cff0 1128       3       sun.security.util.math.intpoly.P256OrderField::carryReduce0 (2216 bytes)
Event: 0.510 Thread 0x0000018e9b5c72c0 1130       4       sun.security.util.math.intpoly.IntegerPolynomial::carryValue (17 bytes)
Event: 0.510 Thread 0x0000018e9b5c72c0 nmethod 1130 0x0000018e87b2b810 code [0x0000018e87b2b980, 0x0000018e87b2ba28]
Event: 0.511 Thread 0x0000018efd09cff0 nmethod 1128 0x0000018e801ca710 code [0x0000018e801ca8e0, 0x0000018e801cbb50]
Event: 0.511 Thread 0x0000018efd09cff0 1129       3       sun.security.util.math.intpoly.P256OrderField::carryReduce2 (518 bytes)
Event: 0.511 Thread 0x0000018efd09cff0 nmethod 1129 0x0000018e801cbc90 code [0x0000018e801cbe40, 0x0000018e801cc390]
Event: 0.512 Thread 0x0000018efd09cff0 1131       3       sun.security.provider.ByteArrayAccess::b2iBig64 (231 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.013 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (20 events):
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: trap_request=0xffffff66 fr.pc=0x0000018e87b25f7c relative=0x000000000000031c
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: reason=speculate_class_check action=maybe_recompile pc=0x0000018e87b25f7c method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT PACKING pc=0x0000018e87b25f7c sp=0x0000001c5f5fe2b0
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT UNPACKING pc=0x0000018e875846a2 sp=0x0000001c5f5fe1f0 mode 2
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018e87b20240 relative=0x0000000000000280
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018e87b20240 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT PACKING pc=0x0000018e87b20240 sp=0x0000001c5f5fe220
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT UNPACKING pc=0x0000018e875846a2 sp=0x0000001c5f5fe1f0 mode 2
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018e87b20240 relative=0x0000000000000280
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018e87b20240 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT PACKING pc=0x0000018e87b20240 sp=0x0000001c5f5fe220
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT UNPACKING pc=0x0000018e875846a2 sp=0x0000001c5f5fe1f0 mode 2
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018e87b20240 relative=0x0000000000000280
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018e87b20240 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT PACKING pc=0x0000018e87b20240 sp=0x0000001c5f5fe220
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT UNPACKING pc=0x0000018e875846a2 sp=0x0000001c5f5fe1f0 mode 2
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018e87b20240 relative=0x0000000000000280
Event: 0.485 Thread 0x0000018e9b3a6be0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018e87b20240 method=sun.security.util.math.intpoly.IntegerPolynomial.multByInt([JJ)V @ 26 c2
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT PACKING pc=0x0000018e87b20240 sp=0x0000001c5f5fe220
Event: 0.485 Thread 0x0000018e9b3a6be0 DEOPT UNPACKING pc=0x0000018e875846a2 sp=0x0000001c5f5fe1f0 mode 2

Classes loaded (20 events):
Event: 0.511 Loading class sun/security/ssl/Finished$T10VerifyDataGenerator
Event: 0.511 Loading class sun/security/ssl/Finished$T10VerifyDataGenerator done
Event: 0.511 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator
Event: 0.511 Loading class sun/security/ssl/Finished$T12VerifyDataGenerator done
Event: 0.511 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator
Event: 0.512 Loading class sun/security/ssl/Finished$T13VerifyDataGenerator done
Event: 0.512 Loading class sun/security/ssl/Finished$1
Event: 0.512 Loading class sun/security/ssl/Finished$1 done
Event: 0.512 Loading class sun/security/ssl/SSLBasicKeyDerivation
Event: 0.512 Loading class sun/security/ssl/SSLBasicKeyDerivation done
Event: 0.512 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec
Event: 0.512 Loading class sun/security/ssl/SSLBasicKeyDerivation$SecretSizeSpec done
Event: 0.513 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 0.513 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 0.513 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt
Event: 0.514 Loading class com/sun/crypto/provider/GaloisCounterMode$GCMEncrypt done
Event: 0.514 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp
Event: 0.514 Loading class com/sun/crypto/provider/GaloisCounterMode$EncryptOp done
Event: 0.514 Loading class sun/security/ssl/CipherSuite$1
Event: 0.514 Loading class sun/security/ssl/CipherSuite$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.275 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x000000062376f348}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x000000062376f348) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.276 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623776958}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000623776958) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.276 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x000000062377d518}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x000000062377d518) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.276 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623783fa8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623783fa8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.277 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623788630}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000623788630) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.280 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b5410}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006237b5410) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.282 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237c4730}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237c4730) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.284 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237f6820}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237f6820) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.285 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237fd0f0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237fd0f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.285 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623000c90}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623000c90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.347 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623270bd0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000623270bd0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.366 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f0bd8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233f0bd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.366 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233f4540}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233f4540) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.370 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c22e20}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c22e20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.382 Thread 0x0000018ef32d3690 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622ca4e68}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622ca4e68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.383 Thread 0x0000018e9b3416b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623212510}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000623212510) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.389 Thread 0x0000018e9b3416b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062324bb48}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062324bb48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.390 Thread 0x0000018e9b3a6be0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1a950}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1a950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.395 Thread 0x0000018e9b3a6be0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6c808}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6c808) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.480 Thread 0x0000018e9b3a6be0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622f83e38}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x0000000622f83e38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (10 events):
Event: 0.096 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.096 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.105 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.105 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.228 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.228 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.385 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.385 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.483 Executing VM operation: ICBufferFull
Event: 0.483 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.054 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd011d50
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd06b9d0
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd0855e0
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd086040
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd086aa0
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd087fd0
Event: 0.055 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd09cff0
Event: 0.076 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd22e060
Event: 0.079 Thread 0x0000018ef32d3690 Thread added: 0x0000018efd22eae0
Event: 0.086 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.088 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.093 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.147 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.306 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.338 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.343 Thread 0x0000018ef32d3690 Thread added: 0x0000018e9b3416b0
Event: 0.388 Thread 0x0000018e9b3416b0 Thread added: 0x0000018e9b3a6be0
Event: 0.399 Thread 0x0000018e9b3416b0 Thread added: 0x0000018e9b41f6a0
Event: 0.431 Thread 0x0000018efd09cff0 Thread added: 0x0000018e9b526990
Event: 0.432 Thread 0x0000018efd09cff0 Thread added: 0x0000018e9b5c72c0


Dynamic libraries:
0x00007ff68fe90000 - 0x00007ff68fe9a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffa60150000 - 0x00007ffa60367000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa5e030000 - 0x00007ffa5e0f4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa5d640000 - 0x00007ffa5d9f9000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa5dac0000 - 0x00007ffa5dbd1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff9c5d90000 - 0x00007ff9c5da8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ff9c5dd0000 - 0x00007ff9c5deb000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffa5de70000 - 0x00007ffa5e01e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa5d4f0000 - 0x00007ffa5d516000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa5f300000 - 0x00007ffa5f329000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa5d520000 - 0x00007ffa5d63b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa5d450000 - 0x00007ffa5d4ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa30f50000 - 0x00007ffa311e2000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96\COMCTL32.dll
0x00007ffa5e6c0000 - 0x00007ffa5e767000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa5f260000 - 0x00007ffa5f291000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000061f00000 - 0x0000000061f0d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffa5eb40000 - 0x00007ffa5ebf2000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa5ec00000 - 0x00007ffa5eca7000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa5dc50000 - 0x00007ffa5dc78000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffa5e520000 - 0x00007ffa5e634000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa49d10000 - 0x00007ffa49e13000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffa5f810000 - 0x00007ffa60086000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa5f7b0000 - 0x00007ffa5f80e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa5cda0000 - 0x00007ffa5cdaa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff9c5df0000 - 0x00007ff9c5dfc000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ff9c6260000 - 0x00007ff9c62ed000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ff9c4b80000 - 0x00007ff9c5937000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffa5e640000 - 0x00007ffa5e6b1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa5d320000 - 0x00007ffa5d36d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa50240000 - 0x00007ffa50274000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa5d300000 - 0x00007ffa5d313000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa5c3c0000 - 0x00007ffa5c3d8000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff9c5dc0000 - 0x00007ff9c5dca000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffa5acc0000 - 0x00007ffa5aef2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa5ece0000 - 0x00007ffa5f06f000 	C:\WINDOWS\System32\combase.dll
0x00007ffa5ea60000 - 0x00007ffa5eb37000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa47fc0000 - 0x00007ffa47ff2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa5ddf0000 - 0x00007ffa5de6b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff9c5d70000 - 0x00007ff9c5d8f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ff9c5d50000 - 0x00007ff9c5d68000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffa5b2f0000 - 0x00007ffa5bbf3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa5b1b0000 - 0x00007ffa5b2ef000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffa5f160000 - 0x00007ffa5f259000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffa5d380000 - 0x00007ffa5d3ab000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ff9c5d40000 - 0x00007ff9c5d50000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffa57690000 - 0x00007ffa577c6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa5c840000 - 0x00007ffa5c8a9000 	C:\WINDOWS\system32\mswsock.dll
0x00007ff9c5d20000 - 0x00007ff9c5d36000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ffa5caa0000 - 0x00007ffa5cabb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa5c320000 - 0x00007ffa5c355000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa5c930000 - 0x00007ffa5c958000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa5ca90000 - 0x00007ffa5ca9c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa5cdb0000 - 0x00007ffa5cddd000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa5ecb0000 - 0x00007ffa5ecb9000 	C:\WINDOWS\System32\NSI.dll
0x00007ff9c5c90000 - 0x00007ff9c5c9e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ffa5dc80000 - 0x00007ffa5dde6000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffa5cd60000 - 0x00007ffa5cd8d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffa5cd20000 - 0x00007ffa5cd57000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffa2c0d0000 - 0x00007ffa2c0d8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ff9c5b00000 - 0x00007ff9c5b09000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 86856K (0% of 33293192K total physical memory with 5631860K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 16178K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1637K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16448B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 7 days 0:22 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (5499M free)
TotalPageFile size 42752M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 84M, peak: 84M
current process commit charge ("private bytes"): 627M, peak: 628M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
