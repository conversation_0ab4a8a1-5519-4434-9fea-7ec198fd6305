spec:
  inputs:
    stage:
      default: build
      description: 执行阶段
      options:
        - "build"
        - "release"
    java_version:
      default: "8"
      description: Java版本 (8/11)
      options:
        - "8"
        - "11"
    skip_tests:
      type: boolean
      default: true
      description: 是否跳过测试
---

include:
  - component: $CI_SERVER_FQDN/devops/gitlab-cicd/sharelib-template@v1.0.0
    inputs:
      stage: $[[ inputs.stage ]]
      java_version: $[[ inputs.java_version ]]
      skip_tests: $[[ inputs.skip_tests ]]

stages:
  - test
  - build
  - release
