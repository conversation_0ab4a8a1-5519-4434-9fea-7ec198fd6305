#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=19768, tid=24936
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Fri Nov 29 14:56:01 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.116854 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002597c283490):  JavaThread "main"             [_thread_in_vm, id=24936, stack(0x0000009ae8f00000,0x0000009ae9000000) (1024K)]

Stack: [0x0000009ae8f00000,0x0000009ae9000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x8bba1e]
V  [jvm.dll+0x684b95]
V  [jvm.dll+0x1ea641]
V  [jvm.dll+0x1ea40e]
V  [jvm.dll+0x6874a8]
V  [jvm.dll+0x6872e2]
V  [jvm.dll+0x68554e]
V  [jvm.dll+0x272c66]
V  [jvm.dll+0x21d63b]
V  [jvm.dll+0x212b29]
V  [jvm.dll+0x5c2c23]
V  [jvm.dll+0x838632]
V  [jvm.dll+0x838584]
V  [jvm.dll+0x47f00c]
V  [jvm.dll+0x4925d6]
C  [java.dll+0x1657]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.defineClass0(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;+0 java.base@21.0.4
j  java.lang.System$2.defineClass(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;+17 java.base@21.0.4
j  java.lang.invoke.MethodHandles$Lookup$ClassDefiner.defineClass(ZLjava/lang/Object;)Ljava/lang/Class;+57 java.base@21.0.4
j  java.lang.invoke.InvokerBytecodeGenerator.loadMethod([B)Ljava/lang/invoke/MemberName;+22 java.base@21.0.4
j  java.lang.invoke.InvokerBytecodeGenerator.generateCustomizedCode(Ljava/lang/invoke/LambdaForm;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MemberName;+30 java.base@21.0.4
j  java.lang.invoke.LambdaForm.compileToBytecode()V+69 java.base@21.0.4
j  java.lang.invoke.LambdaForm.prepare()V+21 java.base@21.0.4
j  java.lang.invoke.MethodHandle.<init>(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;)V+33 java.base@21.0.4
j  java.lang.invoke.BoundMethodHandle.<init>(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;)V+3 java.base@21.0.4
j  java.lang.invoke.BoundMethodHandle$Species_L.<init>(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;Ljava/lang/Object;)V+3 java.base@21.0.4
j  java.lang.invoke.BoundMethodHandle$Species_L.make(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;Ljava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+7 java.base@21.0.4
j  java.lang.invoke.BoundMethodHandle.bindSingle(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;Ljava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+3 java.base@21.0.4
j  java.lang.invoke.SimpleMethodHandle.copyWithExtendL(Ljava/lang/invoke/MethodType;Ljava/lang/invoke/LambdaForm;Ljava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+3 java.base@21.0.4
j  java.lang.invoke.LambdaFormEditor.bindArgumentL(Ljava/lang/invoke/BoundMethodHandle;ILjava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+55 java.base@21.0.4
j  java.lang.invoke.BoundMethodHandle.bindArgumentL(ILjava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+7 java.base@21.0.4
j  java.lang.invoke.MethodHandle.bindArgumentL(ILjava/lang/Object;)Ljava/lang/invoke/BoundMethodHandle;+6 java.base@21.0.4
j  java.lang.invoke.MethodHandle.bindTo(Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;+15 java.base@21.0.4
j  java.lang.invoke.MethodHandles.constant(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/invoke/MethodHandle;+84 java.base@21.0.4
j  java.lang.invoke.InnerClassLambdaMetafactory.buildCallSite()Ljava/lang/invoke/CallSite;+103 java.base@21.0.4
j  java.lang.invoke.LambdaMetafactory.metafactory(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/CallSite;+67 java.base@21.0.4
j  java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+20 java.base@21.0.4
j  java.lang.invoke.Invokers$Holder.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+28 java.base@21.0.4
j  java.lang.invoke.BootstrapMethodInvoker.invoke(Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;+319 java.base@21.0.4
j  java.lang.invoke.CallSite.makeSite(Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/invoke/CallSite;+8 java.base@21.0.4
j  java.lang.invoke.MethodHandleNatives.linkCallSiteImpl(Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/invoke/MemberName;+6 java.base@21.0.4
j  java.lang.invoke.MethodHandleNatives.linkCallSite(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/invoke/MemberName;+45 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000002590b38100d
j  java.security.Security.<clinit>()V+9 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000002590b38100d
j  sun.security.jca.ProviderList.<init>()V+45 java.base@21.0.4
j  sun.security.jca.ProviderList$2.run()Lsun/security/jca/ProviderList;+4 java.base@21.0.4
j  sun.security.jca.ProviderList$2.run()Ljava/lang/Object;+1 java.base@21.0.4
j  java.security.AccessController.executePrivileged(Ljava/security/PrivilegedAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;+29 java.base@21.0.4
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedAction;)Ljava/lang/Object;+5 java.base@21.0.4
j  sun.security.jca.ProviderList.fromSecurityProperties()Lsun/security/jca/ProviderList;+7 java.base@21.0.4
j  sun.security.jca.Providers.<clinit>()V+16 java.base@21.0.4
v  ~StubRoutines::call_stub 0x000002590b38100d
j  sun.security.jca.GetInstance.getInstance(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/String;)Lsun/security/jca/GetInstance$Instance;+0 java.base@21.0.4
j  javax.net.ssl.SSLContext.getInstance(Ljava/lang/String;)Ljavax/net/ssl/SSLContext;+12 java.base@21.0.4
j  externalApp.ExternalAppUtil.sendIdeRequest(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)LexternalApp/ExternalAppUtil$Result;+34
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+37
v  ~StubRoutines::call_stub 0x000002590b38100d

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002591d174e80, length=11, elements={
0x000002597c283490, 0x000002597f5ee500, 0x000002591cfab150, 0x000002591cfc81d0,
0x000002591cfc8c30, 0x000002591cfc9690, 0x000002591cfca4f0, 0x000002591cfd99c0,
0x000002591cfe58e0, 0x000002591d197450, 0x000002591d199110
}

Java Threads: ( => current thread )
=>0x000002597c283490 JavaThread "main"                              [_thread_in_vm, id=24936, stack(0x0000009ae8f00000,0x0000009ae9000000) (1024K)]
  0x000002597f5ee500 JavaThread "Reference Handler"          daemon [_thread_blocked, id=32964, stack(0x0000009ae9700000,0x0000009ae9800000) (1024K)]
  0x000002591cfab150 JavaThread "Finalizer"                  daemon [_thread_blocked, id=28020, stack(0x0000009ae9800000,0x0000009ae9900000) (1024K)]
  0x000002591cfc81d0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=36120, stack(0x0000009ae9900000,0x0000009ae9a00000) (1024K)]
  0x000002591cfc8c30 JavaThread "Attach Listener"            daemon [_thread_blocked, id=43060, stack(0x0000009ae9a00000,0x0000009ae9b00000) (1024K)]
  0x000002591cfc9690 JavaThread "Service Thread"             daemon [_thread_blocked, id=41428, stack(0x0000009ae9b00000,0x0000009ae9c00000) (1024K)]
  0x000002591cfca4f0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=44320, stack(0x0000009ae9c00000,0x0000009ae9d00000) (1024K)]
  0x000002591cfd99c0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=42480, stack(0x0000009ae9d00000,0x0000009ae9e00000) (1024K)]
  0x000002591cfe58e0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=41540, stack(0x0000009ae9e00000,0x0000009ae9f00000) (1024K)]
  0x000002591d197450 JavaThread "Notification Thread"        daemon [_thread_blocked, id=37304, stack(0x0000009ae9f00000,0x0000009aea000000) (1024K)]
  0x000002591d199110 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30956, stack(0x0000009aea000000,0x0000009aea100000) (1024K)]
Total: 11

Other Threads:
  0x000002591cf006b0 VMThread "VM Thread"                           [id=40804, stack(0x0000009ae9600000,0x0000009ae9700000) (1024K)]
  0x000002597f59b770 WatcherThread "VM Periodic Task Thread"        [id=44876, stack(0x0000009ae9500000,0x0000009ae9600000) (1024K)]
  0x000002597f430980 WorkerThread "GC Thread#0"                     [id=36432, stack(0x0000009ae9000000,0x0000009ae9100000) (1024K)]
  0x000002597c2fa8d0 ConcurrentGCThread "G1 Main Marker"            [id=8648, stack(0x0000009ae9100000,0x0000009ae9200000) (1024K)]
  0x000002597c2fb580 WorkerThread "G1 Conc#0"                       [id=12588, stack(0x0000009ae9200000,0x0000009ae9300000) (1024K)]
  0x000002597f4e0650 ConcurrentGCThread "G1 Refine#0"               [id=42924, stack(0x0000009ae9300000,0x0000009ae9400000) (1024K)]
  0x000002597f4e0fd0 ConcurrentGCThread "G1 Service"                [id=41780, stack(0x0000009ae9400000,0x0000009ae9500000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb73c74748] Metaspace_lock - owner thread: 0x000002597c283490

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 0K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 7006K, committed 7040K, reserved 1114112K
  class space    used 553K, committed 576K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622c00000, 0x0000000623000000|  0%| F|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Untracked 
| 125|0x0000000623000000, 0x0000000623000000, 0x0000000623400000|  0%| F|  |TAMS 0x0000000623000000| PB 0x0000000623000000| Untracked 
| 126|0x0000000623400000, 0x0000000623400000, 0x0000000623800000|  0%| F|  |TAMS 0x0000000623400000| PB 0x0000000623400000| Untracked 
| 127|0x0000000623800000, 0x0000000623b5c4e0, 0x0000000623c00000| 84%| E|  |TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x000002597e370000,0x000002597f360000] _byte_map_base: 0x000002597b352000

Marking Bits: (CMBitMap*) 0x000002597c2ea1e0
 Bits: [0x0000025912e50000, 0x000002591ad60000)

Polling page: 0x000002597aa40000

Metaspace:

Usage:
  Non-class:      6.30 MB used.
      Class:    553.90 KB used.
       Both:      6.84 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       6.31 MB ( 10%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     576.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       6.88 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  9.00 MB
       Class:  15.25 MB
        Both:  24.25 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 6.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 110.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 22.
num_chunk_merges: 0.
num_chunk_splits: 8.
num_chunks_enlarged: 2.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=28Kb max_used=28Kb free=119971Kb
 bounds [0x000002590b920000, 0x000002590bb90000, 0x0000025912e50000]
CodeHeap 'profiled nmethods': size=120000Kb used=152Kb max_used=152Kb free=119847Kb
 bounds [0x0000025903e50000, 0x00000259040c0000, 0x000002590b380000]
CodeHeap 'non-nmethods': size=5760Kb used=1290Kb max_used=1304Kb free=4469Kb
 bounds [0x000002590b380000, 0x000002590b5f0000, 0x000002590b920000]
 total_blobs=527 nmethods=121 adapters=311
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.100 Thread 0x000002591cfe58e0 nmethod 101 0x0000025903e73d10 code [0x0000025903e73ec0, 0x0000025903e74140]
Event: 0.106 Thread 0x000002591cfd99c0  104       4       java.util.concurrent.ConcurrentHashMap::spread (10 bytes)
Event: 0.106 Thread 0x000002591cfd99c0 nmethod 104 0x000002590b924210 code [0x000002590b924380, 0x000002590b9243f8]
Event: 0.106 Thread 0x000002591cfe58e0  107       1       java.lang.Enum::ordinal (5 bytes)
Event: 0.106 Thread 0x000002591cfe58e0 nmethod 107 0x000002590b924b10 code [0x000002590b924ca0, 0x000002590b924d68]
Event: 0.109 Thread 0x000002591cfd99c0  108       4       java.lang.String::hashCode (60 bytes)
Event: 0.110 Thread 0x000002591cfe58e0  110       3       java.util.concurrent.ConcurrentHashMap::get (162 bytes)
Event: 0.110 Thread 0x000002591cfd99c0 nmethod 108 0x000002590b925110 code [0x000002590b9252c0, 0x000002590b9255c8]
Event: 0.110 Thread 0x000002591cfe58e0 nmethod 110 0x0000025903e74210 code [0x0000025903e74460, 0x0000025903e74f18]
Event: 0.110 Thread 0x000002591cfe58e0  109       1       jdk.internal.util.StrongReferenceKey::get (5 bytes)
Event: 0.110 Thread 0x000002591cfe58e0 nmethod 109 0x000002590b926090 code [0x000002590b926220, 0x000002590b9262f0]
Event: 0.112 Thread 0x000002591cfe58e0  116       1       java.lang.invoke.MethodType::returnType (5 bytes)
Event: 0.112 Thread 0x000002591cfe58e0 nmethod 116 0x000002590b926690 code [0x000002590b926820, 0x000002590b9268f0]
Event: 0.112 Thread 0x000002591cfe58e0  118       3       java.lang.ref.ReferenceQueue::headIsNull (13 bytes)
Event: 0.112 Thread 0x000002591cfe58e0 nmethod 118 0x0000025903e75210 code [0x0000025903e753a0, 0x0000025903e75510]
Event: 0.112 Thread 0x000002591cfe58e0  119       3       jdk.internal.util.ReferencedKeyMap::removeStaleReferences (30 bytes)
Event: 0.113 Thread 0x000002591cfe58e0 nmethod 119 0x0000025903e75590 code [0x0000025903e75760, 0x0000025903e75ad0]
Event: 0.113 Thread 0x000002591cfe58e0  120   !   3       java.lang.ref.NativeReferenceQueue::poll (28 bytes)
Event: 0.113 Thread 0x000002591cfe58e0 nmethod 120 0x0000025903e75c10 code [0x0000025903e75de0, 0x0000025903e76140]
Event: 0.114 Thread 0x000002591cfd99c0  121       4       java.lang.String::charAt (25 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.008 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.012 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.112 Loading class java/lang/invoke/DelegatingMethodHandle$Holder
Event: 0.113 Loading class java/lang/invoke/DelegatingMethodHandle$Holder done
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor done
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor$TransformKey
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor$TransformKey done
Event: 0.113 Loading class java/lang/invoke/LambdaFormBuffer
Event: 0.113 Loading class java/lang/invoke/LambdaFormBuffer done
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor$Transform
Event: 0.113 Loading class java/lang/invoke/LambdaFormEditor$Transform done
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/Frame
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/Frame done
Event: 0.114 Loading class java/lang/invoke/InvokerBytecodeGenerator$ClassData
Event: 0.114 Loading class java/lang/invoke/InvokerBytecodeGenerator$ClassData done
Event: 0.114 Loading class java/util/ArrayList$Itr
Event: 0.114 Loading class java/util/ArrayList$Itr done
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/FieldWriter
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/FieldVisitor
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/FieldVisitor done
Event: 0.114 Loading class jdk/internal/org/objectweb/asm/FieldWriter done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.093 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.093 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.101 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.101 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (14 events):
Event: 0.011 Thread 0x000002597c283490 Thread added: 0x000002597c283490
Event: 0.051 Thread 0x000002597c283490 Thread added: 0x000002597f5ee500
Event: 0.051 Thread 0x000002597c283490 Thread added: 0x000002591cfab150
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfc81d0
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfc8c30
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfc9690
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfca4f0
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfd99c0
Event: 0.052 Thread 0x000002597c283490 Thread added: 0x000002591cfe58e0
Event: 0.072 Thread 0x000002597c283490 Thread added: 0x000002591d197450
Event: 0.076 Thread 0x000002597c283490 Thread added: 0x000002591d199110
Event: 0.082 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.084 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.090 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll


Dynamic libraries:
0x00007ff6fdce0000 - 0x00007ff6fdcea000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffc144d0000 - 0x00007ffc146e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc13b50000 - 0x00007ffc13c14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc11850000 - 0x00007ffc11c09000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffc11ec0000 - 0x00007ffc11fd1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbca0d0000 - 0x00007ffbca0e8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffbe2370000 - 0x00007ffbe238b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffc13980000 - 0x00007ffc13b2e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc11c10000 - 0x00007ffc11c36000 	C:\WINDOWS\System32\win32u.dll
0x00007ffc13da0000 - 0x00007ffc13dc9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc11ce0000 - 0x00007ffc11dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffc12150000 - 0x00007ffc121ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbe48f0000 - 0x00007ffbe4b82000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96\COMCTL32.dll
0x00007ffc12970000 - 0x00007ffc12a17000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc12800000 - 0x00007ffc12831000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000067d70000 - 0x0000000067d7d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffc138c0000 - 0x00007ffc13972000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc12590000 - 0x00007ffc12637000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc11cb0000 - 0x00007ffc11cd8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffc13c80000 - 0x00007ffc13d94000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbfd3e0000 - 0x00007ffbfd4e3000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffc12a80000 - 0x00007ffc132f6000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffc12a20000 - 0x00007ffc12a7e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffc11160000 - 0x00007ffc1116a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbe2390000 - 0x00007ffbe239c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffbb2e70000 - 0x00007ffbb2efd000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffb72fb0000 - 0x00007ffb73d67000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffc12300000 - 0x00007ffc12371000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffc116a0000 - 0x00007ffc116ed000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffc080f0000 - 0x00007ffc08124000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffc11680000 - 0x00007ffc11693000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffc10780000 - 0x00007ffc10798000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffbdd0d0000 - 0x00007ffbdd0da000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffc0f210000 - 0x00007ffc0f442000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc13fa0000 - 0x00007ffc1432f000 	C:\WINDOWS\System32\combase.dll
0x00007ffc13360000 - 0x00007ffc13437000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbfa7a0000 - 0x00007ffbfa7d2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc117d0000 - 0x00007ffc1184b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbca0b0000 - 0x00007ffbca0cf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ffbc9d90000 - 0x00007ffbc9da8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffc0f680000 - 0x00007ffc0ff83000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffc0f540000 - 0x00007ffc0f67f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffc13df0000 - 0x00007ffc13ee9000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffc11700000 - 0x00007ffc1172b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffbdd0b0000 - 0x00007ffbdd0c0000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffc0ba50000 - 0x00007ffc0bb86000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffc10c00000 - 0x00007ffc10c69000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffbc9d70000 - 0x00007ffbc9d86000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 9, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 40144K (0% of 33293192K total physical memory with 8967408K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 6990K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 7 days 4:42 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (8757M free)
TotalPageFile size 42752M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 39M, peak: 39M
current process commit charge ("private bytes"): 601M, peak: 602M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
