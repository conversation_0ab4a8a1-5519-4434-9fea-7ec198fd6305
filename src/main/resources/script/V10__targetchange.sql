CREATE TABLE `dws_slm_shipment_target_dealer_productline_di` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `dealer_code` VARCHAR(32) COMMENT '经销商代码',
  `geo_code` VARCHAR(32) COMMENT '地理代码',
  `product_code` VARCHAR(32) COMMENT '产品代码',
  `shipment_target_dealer_subregion_productline_krmb_1d` DECIMAL(38, 18) COMMENT '经销商子区域产品线发货目标金额（1天）',
  `shipment_target_dealer_code_productline_krmb_1d` DECIMAL(38, 18) COMMENT '经销商代码产品线发货目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_dealer_code` (`dealer_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);
CREATE TABLE `dws_slm_shipment_target_dealer_productline_di_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `dealer_code` VARCHAR(32) COMMENT '经销商代码',
  `geo_code` VARCHAR(32) COMMENT '地理代码',
  `product_code` VARCHAR(32) COMMENT '产品代码',
  `shipment_target_dealer_subregion_productline_krmb_1d` DECIMAL(38, 18) COMMENT '经销商子区域产品线发货目标金额（1天）',
  `shipment_target_dealer_code_productline_krmb_1d` DECIMAL(38, 18) COMMENT '经销商代码产品线发货目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_dealer_code` (`dealer_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);


CREATE TABLE `dws_slm_ims_after_target_storecode_producttargetline` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店代码',
  `geo_code` VARCHAR(32) COMMENT '地理代码',
  `product_code` VARCHAR(32) COMMENT '产品代码',
  `slm_ims_after_target_offlinestorecode_producttargetline_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 后目标线下门店代码产品目标线金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);
CREATE TABLE `dws_slm_ims_after_target_storecode_producttargetline_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店代码',
  `geo_code` VARCHAR(32) COMMENT '地理代码',
  `product_code` VARCHAR(32) COMMENT '产品代码',
  `slm_ims_after_target_offlinestorecode_producttargetline_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 后目标线下门店代码产品目标线金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',  
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);



CREATE TABLE `ims_after_target_bychannel_subregion` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_ims_after_target_bychannel_subregion_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 目标后按渠道、子区域、产品报告行金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);

CREATE TABLE `ims_after_target_bychannel_subregion_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_ims_after_target_bychannel_subregion_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 目标后按渠道、子区域、产品报告行金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', 
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);


CREATE TABLE `dws_slm_ims_after_target_ka_customer_productreportline_di` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_ims_after_target_ka_customer_productreportline_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);
CREATE TABLE `dws_slm_ims_after_target_ka_customer_productreportline_di_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_ims_after_target_ka_customer_productreportline_krmb_1d` DECIMAL(38, 18) COMMENT 'SLM IMS 目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);



CREATE TABLE `dws_slm_pos_target_ka_customer_productreportline_di` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_pos_target_ka_customer_productreportline_krmb_1d` DECIMAL(38, 18) COMMENT 'POS目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);

CREATE TABLE `dws_slm_pos_target_ka_customer_productreportline_di_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_date` VARCHAR(12) COMMENT '统计日期',
  `store_code` VARCHAR(32) COMMENT '门店编码',
  `geo_code` VARCHAR(32) COMMENT '地理编码',
  `product_code` VARCHAR(32) COMMENT '产品编码',
  `slm_pos_target_ka_customer_productreportline_krmb_1d` DECIMAL(38, 18) COMMENT 'POS目标金额（1天）',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
);