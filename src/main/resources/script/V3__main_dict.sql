drop table if exists `system_dict_type`;
create table `system_dict_type` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`name` varchar(64) NOT NULL COMMENT '字典名称',
	`code` varchar(32)  COMMENT '字典类型编码',
	`is_enabled` bool  COMMENT '是否可用',
	`remark` varchar(64)  COMMENT '备注',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_code_uk(code),
	INDEX ix_code(code) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统字典分类';
drop table if exists `system_dict_data`;
create table `system_dict_data` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`label` varchar(32)  COMMENT '字典标签',
	`value` varchar(64)  COMMENT '字典键值',
	`dict_type_id` int  COMMENT '字典类型ID',
	`sort` int  COMMENT '排序',
	`remark` varchar(64)  COMMENT '备注',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_dict_type_id 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统字典数据';
