INSERT INTO `kpi_indicator` (
    `kpi_name`,
    `code`,
    `group_id`,
    `category_id`,
    `unit`,
    `context`,
    `desc`,
    `is_mtd_support`,
    `is_qtd_support`,
    `is_ytd_support`,
    `sort`,
    `is_enabled`,
    `creator`,
    `updater`
)
VALUES
    ('完美门店-必选分销', 'perfect_store_distribution', 2, 4, '待确认', 1, '完美门店-必选分销', 1, 1, 0, 21, 1, 'manual', 'manual'),
    ('完美门店-主货架', 'perfect_store_shelf', 2, 4, '待确认', 1, '完美门店-主货架', 1, 1, 0, 22, 1, 'manual', 'manual'),
    ('完美门店-陈列占比', 'perfect_store_shelfrate', 2, 4, '待确认', 1, '完美门店-陈列占比', 1, 1, 0, 23, 1, 'manual', 'manual'),
    ('完美门店-POSM', 'perfect_store_posm', 2, 4, '待确认', 1, '完美门店-POSM', 1, 1, 0, 24, 1, 'manual', 'manual');


INSERT INTO `kpi_indicator_detail` (
    `name`,
    `code`,
    `kpi_code`,
    `sql_template_id`,
    `is_mtd_support`,
    `is_qtd_support`,
    `is_ytd_support`,
    `is_personal_support`,
    `is_enabled`,
    `creator`,
    `updater`
)
VALUES
    ('完美门店-必选分销', 'perfect_store_distribution', 'perfect_store_distribution', 30, 1, 1, 0, 0, 1, 'manual', 'manual'),
    ('完美门店-主货架', 'perfect_store_shelf', 'perfect_store_shelf', 31, 1, 1, 0, 0, 1, 'manual', 'manual'),
    ('完美门店-陈列占比', 'perfect_store_shelfrate', 'perfect_store_shelfrate', 32, 1, 1, 0, 0, 1, 'manual', 'manual'),
    ('完美门店-POSM', 'perfect_store_posm', 'perfect_store_posm', 33, 1, 1, 0, 0, 1, 'manual', 'manual');


INSERT INTO `kpi_sql_template` (id, `template_handler`, `area_sql_tmpl`, `store_sql_tmpl`, `is_enabled`, `remark`, `creator`, `updater`)
VALUES
(30,
'PERFECT_STORE_QUERY',
'SELECT
    SUM(distribution_tgt_${time_postfix}) AS target,
    SUM(distribution_act_${time_postfix}) AS actual,
    CASE
        WHEN SUM(distribution_tgt_${time_postfix}) <= 0 THEN NULL
        ELSE SUM(distribution_act_${time_postfix}) / NULLIF(SUM(distribution_tgt_${time_postfix}), 0)
    END AS comp,
    CASE
        WHEN SUM(distribution_act_l${time_postfix}) = 0 THEN NULL
        ELSE (SUM(distribution_act_${time_postfix}) - SUM(distribution_act_l${time_postfix})) / NULLIF(SUM(distribution_act_l${time_postfix}), 0) -1
    END AS _yoy
FROM
    ads_mkt_mobile_kpi_report_perfect_store_m',
NULL, TRUE, '完美门店-必选分销', 'manual', 'manual'),
(31,
'PERFECT_STORE_QUERY',
'SELECT
    SUM(shelf_tgt_${time_postfix}) AS target,
    SUM(shelf_act_${time_postfix}) AS actual,
    CASE
        WHEN SUM(shelf_tgt_${time_postfix}) <= 0 THEN NULL
        ELSE SUM(shelf_act_${time_postfix}) / NULLIF(SUM(shelf_tgt_${time_postfix}), 0)
    END AS comp,
    CASE
        WHEN SUM(shelf_act_l${time_postfix}) = 0 THEN NULL
        ELSE (SUM(shelf_act_${time_postfix}) - SUM(shelf_act_l${time_postfix})) / NULLIF(SUM(shelf_act_l${time_postfix}), 0) -1
    END AS _yoy
FROM
    ads_mkt_mobile_kpi_report_perfect_store_m',
NULL, TRUE, '完美门店-主货架', 'manual', 'manual'),
(32,
'PERFECT_STORE_QUERY',
'SELECT
    SUM(shelfrate_tgt_${time_postfix}) AS target,
    SUM(shelfrate_act_${time_postfix}) AS actual,
    CASE
        WHEN SUM(shelfrate_tgt_${time_postfix}) <= 0 THEN NULL
        ELSE SUM(shelfrate_act_${time_postfix}) / NULLIF(SUM(shelfrate_tgt_${time_postfix}), 0)
    END AS comp,
    CASE
        WHEN SUM(shelfrate_act_l${time_postfix}) = 0 THEN NULL
        ELSE (SUM(shelfrate_act_${time_postfix}) - SUM(shelfrate_act_l${time_postfix})) / NULLIF(SUM(shelfrate_act_l${time_postfix}), 0) -1
    END AS _yoy
FROM
    ads_mkt_mobile_kpi_report_perfect_store_m',
NULL, TRUE, '完美门店-陈列占比', 'manual', 'manual'),
(33,
'PERFECT_STORE_QUERY',
'SELECT
    SUM(posm_tgt_${time_postfix}) AS target,
    SUM(posm_act_${time_postfix}) AS actual,
    CASE
        WHEN SUM(posm_tgt_${time_postfix}) <= 0 THEN NULL
        ELSE SUM(posm_act_${time_postfix}) / NULLIF(SUM(posm_tgt_${time_postfix}), 0)
    END AS comp,
    CASE
        WHEN SUM(posm_act_l${time_postfix}) = 0 THEN NULL
        ELSE (SUM(posm_act_${time_postfix}) - SUM(posm_act_l${time_postfix})) / NULLIF(SUM(posm_act_l${time_postfix}), 0) -1
    END AS _yoy
FROM
    ads_mkt_mobile_kpi_report_perfect_store_m',
NULL, TRUE, '完美门店-POSM', 'manual', 'manual');




INSERT INTO `app_account_role`(`role_type`,`role_name`,`indicator_type`,`indicator_name`,`card_name`,`filter_1`,`filter_2`,`filter_3`)
VALUES
('Area','总部管理层及中台','用户结果','完美门店-必选分销','测试','门店大区','门店区域','门店渠道'),
('Area','总部管理层及中台','用户结果','完美门店-主货架','测试','门店大区','门店区域','门店渠道'),
('Area','总部管理层及中台','用户结果','完美门店-陈列占比','测试','门店大区','门店区域','门店渠道'),
('Area','总部管理层及中台','用户结果','完美门店-POSM','测试','门店大区','门店区域','门店渠道');

INSERT INTO kpi_dim_indicator (id, dim_name, dim_code, sort, is_enabled, creator, updater)
VALUES (23, '门店区域', 'area_name', 9, 1, 'manual', 'manual');

--todo: role卡片表 && setting表配置


