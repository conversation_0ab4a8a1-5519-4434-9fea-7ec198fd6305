drop table if exists `kpi_group`;
create table `kpi_group` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`group_name` varchar(64) NOT NULL COMMENT '分组名称',
	`icon` varchar(128)  COMMENT '图标',
	`is_enabled` bool  COMMENT '是否可用',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_group_name(group_name) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='指标分组';
drop table if exists `kpi_category`;
create table `kpi_category` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`category_name` varchar(64) NOT NULL COMMENT '分类名称',
	`icon` varchar(128)  COMMENT '图标',
	`is_enabled` bool  COMMENT '是否可用',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_category_name(category_name) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='指标分类';
drop table if exists `kpi_indicator`;
create table `kpi_indicator` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`kpi_name` varchar(64) NOT NULL COMMENT '指标名称',
	`code` varchar(64) NOT NULL COMMENT '指标编码',
	`group_id` int  COMMENT '分组id',
	`category_id` int NOT NULL COMMENT '分类ID',
	`desc` varchar(128)  COMMENT '描述',
	`sort` int  COMMENT '排序',
	`is_enabled` bool  default TRUE COMMENT '是否可用',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_group_id(group_id),
	INDEX ix_category_id(category_id) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='指标说明';
drop table if exists `kpi_dim_indicator`;
create table `kpi_dim_indicator` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`dim_name` varchar(64) NOT NULL COMMENT '指标维度名称',
	`dim_code` varchar(32)  COMMENT '维度编码',
	`kpi_indicator_id` int NOT NULL COMMENT '指标id',
	`sort` int  COMMENT '排序',
	`is_enabled` bool  default TRUE COMMENT '是否可用',
	`remark` varchar(64)  COMMENT '备注',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_kpi_indicator_id(kpi_indicator_id) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='kpi维度指标';
drop table if exists `app_announcement`;
create table `app_announcement` ( 
	 `id` int(4) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`start_time` datetime  COMMENT '公告显示开始时间',
	`end_time` datetime  COMMENT '公告显示结束时间',
	`announcement` varchar(1000) NOT NULL COMMENT '公告内容',
	`is_enabled` bool NOT NULL default TRUE COMMENT '是否启用',
	`remark` varchar(500)  COMMENT '备注',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_start_time(start_time),
	INDEX ix_end_time(end_time) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统公告';
drop table if exists `kpi_data_sync_task`;
create table `kpi_data_sync_task` ( 
	 `id` varchar(64) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`task_name` varchar(32) NOT NULL COMMENT '任务名称',
	`source_table_name` varchar(32)  COMMENT '源表格名称',
	`target_table_name` varchar(32)  COMMENT '目标表格名称',
	`is_suc` bool  COMMENT '是否成功',
	`data_time` date NOT NULL COMMENT '数据时间',
	`job_start_time` datetime NOT NULL COMMENT '任务开始时间',
	`job_end_time` datetime NOT NULL COMMENT '任务结束时间',
	`has_finished` bool NOT NULL COMMENT '是否完成',
	`suc_count` int  COMMENT '成功数',
	`failed_count` int  COMMENT '失败数',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间' 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='kpi数据同步任务';
