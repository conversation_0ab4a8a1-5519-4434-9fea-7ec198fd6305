drop table if exists ads_mkt_mobile_kpi_report_sales_store_m;
drop table if exists ads_mkt_mobile_kpi_report_sales_store_m_trans;
CREATE TABLE `ads_mkt_mobile_kpi_report_sales_store_m` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `region_name` VARCHAR(16) COMMENT '大区',
  `subregion_name` VARCHAR(16) COMMENT '省区',
  `area_name` VARCHAR(16) COMMENT '功能区域',
  `active_store_tgt_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当月',
  `active_store_act_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当月',
  `active_store_tgt_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当季',
  `active_store_act_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当季',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`)
) ENGINE=InnoDB COMMENT='移动业绩KPI报告_月报_活跃门店指标';
CREATE TABLE `ads_mkt_mobile_kpi_report_sales_store_m_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `region_name` VARCHAR(16) COMMENT '大区',
  `subregion_name` VARCHAR(16) COMMENT '省区',
  `area_name` VARCHAR(16) COMMENT '功能区域',
  `active_store_tgt_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当月',
  `active_store_act_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当月',
  `active_store_tgt_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当季',
  `active_store_act_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当季',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`)
) ENGINE=InnoDB COMMENT='移动业绩KPI报告_月报_活跃门店指标';


drop table if exists dws_mkt_special_nua_target_di;
drop table if exists dws_mkt_special_nua_target_di_trans;
CREATE TABLE `dws_mkt_special_nua_target_di` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `region_name` VARCHAR(16) COMMENT '医务代表大区',
  `subregion_name` VARCHAR(16) COMMENT '医务代表省区',
  `city_name` VARCHAR(16) COMMENT '销售城市',
  `pne_code` VARCHAR(64) COMMENT 'PNE工号',
  `pen_name` VARCHAR(64) COMMENT 'PNE姓名',
  `pne_email` VARCHAR(64) COMMENT 'PNE邮箱',
  `mkt_target_qty_special_nua_hospital_line` BIGINT COMMENT '特配NUA目标数量',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_pne_code` (`pne_code`),
  INDEX `idx_pen_name` (`pen_name`),
  INDEX `idx_pne_email` (`pne_email`)
) ENGINE=InnoDB COMMENT='NUA特配目标';

CREATE TABLE `dws_mkt_special_nua_target_di_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `region_name` VARCHAR(16) COMMENT '医务代表大区',
  `subregion_name` VARCHAR(16) COMMENT '医务代表省区',
  `city_name` VARCHAR(16) COMMENT '销售城市',
  `pne_code` VARCHAR(64) COMMENT 'PNE工号',
  `pen_name` VARCHAR(64) COMMENT 'PNE姓名',
  `pne_email` VARCHAR(64) COMMENT 'PNE邮箱',
  `mkt_target_qty_special_nua_hospital_line` BIGINT COMMENT '特配NUA目标数量',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_pne_code` (`pne_code`),
  INDEX `idx_pen_name` (`pen_name`),
  INDEX `idx_pne_email` (`pne_email`)
) ENGINE=InnoDB COMMENT='NUA特配目标';



drop table if exists ads_mkt_mobile_kpi_report_area_pnep;
drop table if exists ads_mkt_mobile_kpi_report_area_pnep_trans;
CREATE TABLE `ads_mkt_mobile_kpi_report_area_pnep` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(12) COMMENT '数据年份',
  `date_type` VARCHAR(12) COMMENT '日期类型',
  `pne_region_name` VARCHAR(64) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(64) COMMENT '医务代表省区',
  `city_name` VARCHAR(64) COMMENT '销售城市',
  `qty_hospital_visit` BIGINT COMMENT '目标医院覆盖数',
  `qty_hospital_target` BIGINT COMMENT '目标医院数',
  `qty_act_hcp` BIGINT COMMENT 'HCP教育活动场次',
  `qty_act_hcp_target` BIGINT COMMENT '计划数-HCP教育活动场次',
  `qty_act_hcp_sign` BIGINT COMMENT 'HCP教育活动覆盖人数',
  `qty_act_hcp_sign_target` BIGINT COMMENT '计划数-HCP教育活动覆盖人数',
  `qty_hospital_visit_ly` BIGINT COMMENT '去年目标医院覆盖数',
  `qty_act_hcp_ly` BIGINT COMMENT '去年HCP教育活动场次',
  `qty_act_hcp_sign_ly` BIGINT COMMENT '去年HCP教育活动覆盖人数',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_date_type` (`date_type`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_city_name` (`city_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PNE - 区域维度';
CREATE TABLE `ads_mkt_mobile_kpi_report_area_pnep_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(12) COMMENT '数据年份',
  `date_type` VARCHAR(12) COMMENT '日期类型',
  `pne_region_name` VARCHAR(64) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(64) COMMENT '医务代表省区',
  `city_name` VARCHAR(64) COMMENT '销售城市',
  `qty_hospital_visit` BIGINT COMMENT '目标医院覆盖数',
  `qty_hospital_target` BIGINT COMMENT '目标医院数',
  `qty_act_hcp` BIGINT COMMENT 'HCP教育活动场次',
  `qty_act_hcp_target` BIGINT COMMENT '计划数-HCP教育活动场次',
  `qty_act_hcp_sign` BIGINT COMMENT 'HCP教育活动覆盖人数',
  `qty_act_hcp_sign_target` BIGINT COMMENT '计划数-HCP教育活动覆盖人数',
  `qty_hospital_visit_ly` BIGINT COMMENT '去年目标医院覆盖数',
  `qty_act_hcp_ly` BIGINT COMMENT '去年HCP教育活动场次',
  `qty_act_hcp_sign_ly` BIGINT COMMENT '去年HCP教育活动覆盖人数',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(64) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_date_type` (`date_type`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_city_name` (`city_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PNE - 区域维度';

drop table if exists ads_mkt_mobile_kpi_report_person_pnep;
drop table if exists ads_mkt_mobile_kpi_report_person_pnep_trans;
CREATE TABLE `ads_mkt_mobile_kpi_report_person_pnep` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `date_type` VARCHAR(12) COMMENT '日期类型',
  `person_code` VARCHAR(64) COMMENT '工号',
  `person_name` VARCHAR(64) COMMENT '姓名',
  `person_email` VARCHAR(64) COMMENT '邮箱',
  `pne_region_name` VARCHAR(64) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(64) COMMENT '医务代表省区',
  `city_name` VARCHAR(64) COMMENT '销售城市',
  `qty_hospital_visit` BIGINT COMMENT '目标医院覆盖数',
  `qty_hospital_target` BIGINT COMMENT '目标医院数',
  `qty_act_hcp` BIGINT COMMENT 'HCP教育活动场次',
  `qty_act_hcp_target` BIGINT COMMENT '计划数-HCP教育活动场次',
  `qty_act_hcp_sign` BIGINT COMMENT 'HCP教育活动覆盖人数',
  `qty_act_hcp_sign_target` BIGINT COMMENT '计划数-HCP教育活动覆盖人数',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(255) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_date_type` (`date_type`),
  INDEX `idx_person_code` (`person_code`),
  INDEX `idx_person_name` (`person_name`),
  INDEX `idx_person_email` (`person_email`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PNE - 一线人员维度';
CREATE TABLE `ads_mkt_mobile_kpi_report_person_pnep_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `date_type` VARCHAR(12) COMMENT '日期类型',
  `person_code` VARCHAR(64) COMMENT '工号',
  `person_name` VARCHAR(64) COMMENT '姓名',
  `person_email` VARCHAR(64) COMMENT '邮箱',
  `pne_region_name` VARCHAR(64) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(64) COMMENT '医务代表省区',
  `city_name` VARCHAR(64) COMMENT '销售城市',
  `qty_hospital_visit` BIGINT COMMENT '目标医院覆盖数',
  `qty_hospital_target` BIGINT COMMENT '目标医院数',
  `qty_act_hcp` BIGINT COMMENT 'HCP教育活动场次',
  `qty_act_hcp_target` BIGINT COMMENT '计划数-HCP教育活动场次',
  `qty_act_hcp_sign` BIGINT COMMENT 'HCP教育活动覆盖人数',
  `qty_act_hcp_sign_target` BIGINT COMMENT '计划数-HCP教育活动覆盖人数',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(255) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_date_type` (`date_type`),
  INDEX `idx_person_code` (`person_code`),
  INDEX `idx_person_name` (`person_name`),
  INDEX `idx_person_email` (`person_email`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PNE - 一线人员维度';

drop table if exists ims_after_target_salescity_producttargetline_pne_di;
drop table if exists ims_after_target_salescity_producttargetline_pne_di_trans;
CREATE TABLE `ims_after_target_salescity_producttargetline_pne_di` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `stat_date` VARCHAR(12) COMMENT '业务日期',
  `store_code` VARCHAR(24) COMMENT '门店编码',
  `geo_code` VARCHAR(24) COMMENT 'geo_code',
  `product_code` VARCHAR(24) COMMENT '产品编码',
  `slm_ims_after_target_salescity_producttargetline_pne_krmb_1d` DECIMAL(38, 10) COMMENT '目标金额-千元',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(255) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓后IMS_by销售城市by目标产品线byPNE目标表';
CREATE TABLE `ims_after_target_salescity_producttargetline_pne_di_trans` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `stat_date` VARCHAR(12) COMMENT '业务日期',
  `store_code` VARCHAR(24) COMMENT '门店编码',
  `geo_code` VARCHAR(24) COMMENT 'geo_code', 
  `product_code` VARCHAR(24) COMMENT '产品编码',
  `slm_ims_after_target_salescity_producttargetline_pne_krmb_1d` DECIMAL(38, 10) COMMENT '目标金额-千元',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(255) DEFAULT NULL COMMENT '更新人',   
  PRIMARY KEY (`id`),
  INDEX `idx_stat_date` (`stat_date`),
  INDEX `idx_store_code` (`store_code`),
  INDEX `idx_geo_code` (`geo_code`),
  INDEX `idx_product_code` (`product_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓后IMS_by销售城市by目标产品线byPNE目标表';



ALTER TABLE dim_store
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64),
ADD COLUMN pne_area_name VARCHAR(64),
ADD COLUMN pnep_code VARCHAR(64),
ADD COLUMN pnep_name VARCHAR(64),
ADD COLUMN emd_code VARCHAR(64),
ADD COLUMN emd_name VARCHAR(64);
CREATE INDEX idx_pne_region_name ON dim_store(pne_region_name);
CREATE INDEX idx_pne_subregion_name ON dim_store(pne_subregion_name);
CREATE INDEX idx_pnep_code ON dim_store(pnep_code),
CREATE INDEX idx_emd_code ON dim_store(emd_code);
ALTER TABLE dim_store_trans
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64),
ADD COLUMN pne_area_name VARCHAR(64),
ADD COLUMN pnep_code VARCHAR(64),
ADD COLUMN pnep_name VARCHAR(64),
ADD COLUMN emd_code VARCHAR(64),
ADD COLUMN emd_name VARCHAR(64);
CREATE INDEX idx_pne_region_name ON dim_store_trans(pne_region_name);
CREATE INDEX idx_pne_subregion_name ON dim_store_trans(pne_subregion_name);
CREATE INDEX idx_pnep_code ON dim_store_trans(pnep_code);
CREATE INDEX idx_emd_code ON dim_store_trans(emd_code);


-- 正式环境未动
-- ALTER TABLE ads_slm_mobile_kpi_report_person_m
-- ADD COLUMN store_code VARCHAR(24),
-- ADD COLUMN store_name VARCHAR(128);
-- CREATE INDEX idx_store_code ON ads_slm_mobile_kpi_report_person_m (store_code);
-- ALTER TABLE ads_slm_mobile_kpi_report_person_m_trans
-- ADD COLUMN store_code VARCHAR(24),
-- ADD COLUMN store_name VARCHAR(128);
-- CREATE INDEX idx_store_code ON ads_slm_mobile_kpi_report_person_m_trans (store_code);
--
-- ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m
-- ADD COLUMN store_code VARCHAR(24),
-- ADD COLUMN store_name VARCHAR(128);
-- CREATE INDEX idx_store_code ON ads_mkt_mobile_kpi_report_person_nua_m (store_code);
-- ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m_trans
-- ADD COLUMN store_code VARCHAR(24),
-- ADD COLUMN store_name VARCHAR(128);
-- CREATE INDEX idx_store_code ON ads_mkt_mobile_kpi_report_person_nua_m_trans (store_code);




ALTER TABLE ads_slm_mobile_kpi_report_area
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64);
CREATE INDEX idx_pne_region_name ON ads_slm_mobile_kpi_report_area (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_slm_mobile_kpi_report_area (pne_subregion_name);
ALTER TABLE ads_slm_mobile_kpi_report_area_trans
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64);
CREATE INDEX idx_pne_region_name ON ads_slm_mobile_kpi_report_area_trans (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_slm_mobile_kpi_report_area_trans (pne_subregion_name);


ALTER TABLE ads_slm_mobile_kpi_report_person_m
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64),
ADD COLUMN amount_rmb_m_doh_fz DECIMAL(38, 18),
ADD COLUMN amount_rmb_m_doh_fm DECIMAL(38, 18);
CREATE INDEX idx_pne_region_name ON ads_slm_mobile_kpi_report_person_m (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_slm_mobile_kpi_report_person_m (pne_subregion_name);
ALTER TABLE ads_slm_mobile_kpi_report_person_m_trans
ADD COLUMN pne_region_name VARCHAR(64),
ADD COLUMN pne_subregion_name VARCHAR(64),
ADD COLUMN amount_rmb_m_doh_fz DECIMAL(38, 18),
ADD COLUMN amount_rmb_m_doh_fm DECIMAL(38, 18);
CREATE INDEX idx_pne_region_name ON ads_slm_mobile_kpi_report_person_m_trans (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_slm_mobile_kpi_report_person_m_trans (pne_subregion_name);








ALTER TABLE ads_mkt_mobile_kpi_report_area_act_m
ADD COLUMN area_name VARCHAR(16),
ADD COLUMN channelcategory_name VARCHAR(32);
CREATE INDEX idx_area_name ON ads_mkt_mobile_kpi_report_area_act_m (area_name);
CREATE INDEX idx_channelcategory ON ads_mkt_mobile_kpi_report_area_act_m (channelcategory_name);

ALTER TABLE ads_mkt_mobile_kpi_report_area_act_m_trans
ADD COLUMN area_name VARCHAR(16),
ADD COLUMN channelcategory_name VARCHAR(32);
CREATE INDEX idx_area_name ON ads_mkt_mobile_kpi_report_area_act_m_trans (area_name);
CREATE INDEX idx_channelcategory ON ads_mkt_mobile_kpi_report_area_act_m (channelcategory_name);


ALTER TABLE ads_mkt_mobile_kpi_report_area_nua
ADD COLUMN `qty_special_nua_hospital_line` BIGINT,
ADD COLUMN `qty_special_nua_hospital_line_ly` BIGINT;
ALTER TABLE ads_mkt_mobile_kpi_report_area_nua_trans
ADD COLUMN `qty_special_nua_hospital_line` BIGINT,
ADD COLUMN `qty_special_nua_hospital_line_ly` BIGINT;

ALTER TABLE ads_mkt_mobile_kpi_report_area_nua
ADD COLUMN pne_region_name varchar(24),
ADD COLUMN pne_subregion_name varchar(24);
CREATE INDEX idx_pne_region_name ON ads_mkt_mobile_kpi_report_area_nua (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_mkt_mobile_kpi_report_area_nua (pne_subregion_name);

ALTER TABLE ads_mkt_mobile_kpi_report_area_nua_trans
ADD COLUMN `pne_region_name` varchar(24),
ADD COLUMN `pne_subregion_name` varchar(24);
CREATE INDEX idx_pne_region_name ON ads_mkt_mobile_kpi_report_area_nua_trans (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_mkt_mobile_kpi_report_area_nua_trans (pne_subregion_name);


ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m
ADD COLUMN pne_region_name VARCHAR(24),
ADD COLUMN pne_subregion_name VARCHAR(24);
CREATE INDEX idx_pne_region_name ON ads_mkt_mobile_kpi_report_person_nua_m (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_mkt_mobile_kpi_report_person_nua_m (pne_subregion_name);
ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m_trans
ADD COLUMN pne_region_name VARCHAR(24),
ADD COLUMN pne_subregion_name VARCHAR(24);
CREATE INDEX idx_pne_region_name ON ads_mkt_mobile_kpi_report_person_nua_m_trans (pne_region_name);
CREATE INDEX idx_pne_subregion_name ON ads_mkt_mobile_kpi_report_person_nua_m_trans (pne_subregion_name);

ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m
ADD COLUMN qty_special_nua_hospital_line BIGINT;
ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m_trans
ADD COLUMN qty_special_nua_hospital_line BIGINT;

ALTER TABLE dws_actv_activity_target_di
ADD COLUMN area_name VARCHAR(16),
ADD COLUMN channelcategory_name VARCHAR(32);
CREATE INDEX idx_area_name ON dws_actv_activity_target_di (area_name);
CREATE INDEX idx_channelcategory ON dws_actv_activity_target_di (channelcategory_name);
ALTER TABLE dws_actv_activity_target_di_trans
ADD COLUMN area_name VARCHAR(16),
ADD COLUMN channelcategory_name VARCHAR(32);
CREATE INDEX idx_area_name ON dws_actv_activity_target_di_trans (area_name);
CREATE INDEX idx_channelcategory ON dws_actv_activity_target_di_trans (channelcategory_name);


--NC 早阶新客增加目标 dws_slm_nc_offtake_target_store_producttargetline_di（slm_nc_offtake_target_store_nua_early_1d  slm_nc_offtake_target_store_lp_early_krmb_1d ）
ALTER TABLE dws_slm_nc_offtake_target_store_producttargetline_di
ADD COLUMN slm_nc_offtake_target_store_nua_early_1d DECIMAL(38, 10),
ADD COLUMN slm_nc_offtake_target_store_lp_early_krmb_1d DECIMAL(38, 10);  
ALTER TABLE dws_slm_nc_offtake_target_store_producttargetline_di_trans
ADD COLUMN slm_nc_offtake_target_store_nua_early_1d DECIMAL(38, 10),
ADD COLUMN slm_nc_offtake_target_store_lp_early_krmb_1d DECIMAL(38, 10);  


CREATE TABLE dbsyncer_mjn(
   `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `job_code` VARCHAR(128),
  `cron_expr` VARCHAR(24),
  `is_enabled` boolean NOT NULL DEFAULT false,
  `job_id` VARCHAR(64),
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE(job_code)
);

SELECT * FROM `dbsyncer_config` WHERE `name` LIKE '%ims_after_target_salescity_producttargetline%';

INSERT INTO dbsyncer_mjn (job_code, job_id, cron_expr, is_enabled, creator, updater)
VALUES
('ads_mkt_mobile_kpi_report_area_act_m', '1316468945577971712', '0 6 11 * * ?', 1, 'system', 'system'),
('ads_mkt_mobile_kpi_report_area_nua', '1316440689009102848', '0 0 11 * * ?', 1, 'system', 'system'),
('ads_mkt_mobile_kpi_report_person_nua_m', '1316448641405292544', '0 3 11 * * ?', 1, 'system', 'system'),

('dws_actv_activity_target_di', '1317109921883492352', '0 1 11 * * ?', 1, 'system', 'system'),
('ads_slm_mobile_kpi_report_area', '1313932786922557440', '0 45 6 * * ?', 1, 'system', 'system'),
('ads_slm_mobile_kpi_report_person_m', '1314224078361595904', '0 0 7 * * ?', 1, 'system', 'system'),

('dws_slm_shipment_target_dealer_di', '1314178334787244032', '0 1 5 * * ?', 1, 'system', 'system'),
('dws_slm_shipment_target_product_di', '1315674171056984064', '0 2 5 * * ?', 1, 'system', 'system'),
('dws_slm_gt_shipment_target_di', '1315675807905091584', '0 0 5 * * ?', 1, 'system', 'system'),
('dws_slm_ims_after_target_storechannel_productreportline_di', '1314220035513913344', '0 30 1 * * ?', 1, 'system', 'system'),
('dws_slm_ims_after_target_storearea_producttargetline_di', '1314220503497576448', '0 31 1 * * ?', 1, 'system', 'system'),
('dws_slm_ims_after_target_storecustomer_productreportline_di', '1318632522107719680', '0 32 1 * * ?', 1, 'system', 'system'),
('dws_slm_mbx_kpibystoretodasheng_di', '1314203985984491520', '0 35 1 * * ?', 1, 'system', 'system'),
('dws_slm_nc_offtake_target_store_producttargetline_di', '1313939950416302080', '0 40 1 * * ?', 1, 'system', 'system'),
('dws_slm_pos_target_store_product_di', '1313938038535098368', '0 33 1 * * ?', 1, 'system', 'system'),

('ads_mobile_kpi_update_time_d', '1325872938020376576', '5 0 11 * * ?', 1, 'system', 'system'),
('dim_mobile_bi_user_access', '1313943443235540992', '0 35 6 * * ?', 1, 'system', 'system'),
('dim_store', '1316079762657120256', '0 0 2 * * ?', 1, 'system', 'system'),
('dim_product', '1316006627056422912', '0 0 1 * * ?', 1, 'system', 'system'),
('dim_dealer', '1313829313191219200', '0 30 4 * * ?', 1, 'system', 'system'),
('dim_activity', '1337793609126252544', '30 0 1 * * ?', 1, 'system', 'system'),

('ads_mkt_mobile_kpi_report_area_pnep', null, '5 0 1 * * ?', 1, 'system', 'system'),
('ads_mkt_mobile_kpi_report_person_pnep', null, '15 0 1 * * ?', 1, 'system', 'system'),
('ims_after_target_salescity_producttargetline_pne_di', null, '1 0 2 * * ?', 1, 'system', 'system'),
('dws_mkt_special_nua_target_di', null, '0 1 1 * * ?', 1, 'system', 'system'),
('ads_mkt_mobile_kpi_report_sales_store_m', null, '0 1 5 * * ?', 1, 'system', 'system');