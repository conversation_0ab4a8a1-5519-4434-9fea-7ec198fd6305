INSERT INTO `kpi_category` (`id`, `category_name`, `icon`, `is_enabled`, `creator`, `updater`)
VALUES (8, '销售待上', NULL, 1, '1', '1');
INSERT INTO `kpi_category` (`id`, `category_name`, `icon`, `is_enabled`, `creator`, `updater`)
VALUES (9, '业绩待上', NULL, 1, '1', '1');


INSERT INTO `app_account_role`(`role_type`,`role_name`,`indicator_type`,`indicator_name`,`card_name`,`filter_1`,`filter_2`,`filter_3`)
VALUES
('Area','总部管理层及中台','用户结果','月度活跃门店','测试','门店大区','门店区域','门店渠道'),
('Area','总部管理层及中台','用户结果','季度活跃门店','测试','门店大区','门店区域','门店渠道');


INSERT INTO app_account_access (
  stat_month,
  user_code,
  user_name,
  user_name_en,
  user_email,
  role_name,
  role_type,
  access_type, level_1)
  VALUES (
  '202503', -- 假定当前统计月份为2023年12月
  'U1234561', -- 示例工号
  'Jane Liu', -- 姓名
  'Jane Liu', -- 英文名，假设与中文名相同
  '<EMAIL>', -- 邮箱地址
  '医务销售PNE - 大区/区域负责人',
  'Area',
  '组织架构','全国');

  INSERT INTO app_account_access (
  stat_month,
  user_code,
  user_name,
  user_name_en,
  user_email,
  role_name,
  role_type,
  access_type, level_1, level_2, level_3)
  VALUES (
  '202503', -- 假定当前统计月份为2023年12月
  'U1234561', -- 示例工号
  '医务销售PNE - 大区/区域负责人', -- 姓名
  '医务销售PNE - 大区/区域负责人', -- 英文名，假设与中文名相同
  '<EMAIL>', -- 邮箱地址
  '医务销售PNE - 大区/区域负责人',
  'Area',
  '组织架构','线下','华南区','粤西广海');


INSERT INTO app_account_access (
  stat_month,
  user_code,
  user_name,
  user_name_en,
  user_email,
  role_name,
  role_type,
  access_type, level_1, level_2)
  VALUES (
  '202503', -- 假定当前统计月份为2023年12月
  'U1234562', -- 示例工号
  '医务总部及医务市场推广', -- 姓名
  '医务总部及医务市场推广', -- 英文名，假设与中文名相同
  '<EMAIL>', -- 邮箱地址
  '医务销售PNE - 医务总部及医务市场推广',
  'Area',
  '组织架构','线下','华北区');


  INSERT INTO app_account_access (
  stat_month,
  user_code,
  user_name,
  user_name_en,
  user_email,
  role_name,
  role_type,
  access_type, level_1, level_2,level_3,level_4,level_5,level_6)
  VALUES (
  '202503', -- 假定当前统计月份为2023年12月
  'U1234563', -- 示例工号
  '医务销售PNE - 城市负责人', -- 姓名
  '医务销售PNE - 城市负责人', -- 英文名，假设与中文名相同
  '<EMAIL>', -- 邮箱地址
  '医务销售PNE - 城市负责人',
  'STORE',
  '组织架构','线下','华南区','江西','陇南市','随便','666666');


INSERT INTO `mjn_kpi_dashboard`.`app_account_role` (`migrate_key`, `id`, `role_type`, `role_name`, `indicator_type`, `indicator_name`, `card_name`, `filter_1`, `filter_2`, `filter_3`, `filter_4`, `filter_5`, `filter_6`, `remark`, `insert_time`, `creator`, `updater`, `create_time`, `update_time`) VALUES (NULL, 75, 'By Store', 'NKA省区/地区/城市渠道负责人', '零售商销量及库存', 'NC 门店库存天数', '线下', '营业渠道', '连锁客户', '目标产品线', NULL, NULL, NULL, NULL, '2024-12-18 00:40:20', NULL, NULL, '2024-12-18 00:40:20', '2024-12-18 00:40:20');
INSERT INTO `mjn_kpi_dashboard`.`app_account_role` (`migrate_key`, `id`, `role_type`, `role_name`, `indicator_type`, `indicator_name`, `card_name`, `filter_1`, `filter_2`, `filter_3`, `filter_4`, `filter_5`, `filter_6`, `remark`, `insert_time`, `creator`, `updater`, `create_time`, `update_time`) VALUES (NULL, 80, 'By Store', 'NKA省区/地区/城市渠道负责人', '零售商销量及库存', 'NKA零售商库存天数', '线下', '营业渠道', '连锁客户', '目标产品线', NULL, NULL, NULL, NULL, '2024-12-18 00:40:20', NULL, NULL, '2024-12-18 00:40:20', '2024-12-18 00:40:20');
