drop table if exists `app_account`;
create table `app_account` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`userid` varchar(64) NOT NULL COMMENT '企微用户id',
	`code` varchar(64)  COMMENT '员工号',
	`name` varchar(64)  COMMENT '用户名称',
	`name_en` varchar(64)  COMMENT '英文名',
	`gender` int  COMMENT '性别',
	`avatar` varchar(255)  COMMENT '头像url',
	`qr_code` varchar(255)  COMMENT '员工二维码',
	`mobile` varchar(16)  COMMENT '手机号',
	`reg_time` datetime  COMMENT '注册时间',
	`reg_channel` varchar(32)  COMMENT '注册渠道',
	`email` varchar(32)  COMMENT '邮箱',
	`biz_mail` varchar(32)  COMMENT '企业邮箱',
	`address` varchar(255)  COMMENT '地址',
	`is_enabled` bool  COMMENT '是否可用',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_user_uk(userid),
	UNIQUE KEY uk_email_uk(email),
	INDEX ix_userid(userid),
	INDEX ix_email(email),
	INDEX ix_biz_email(biz_mail) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='企微账号';
drop table if exists `app_role`;
create table `app_role` ( 
	 `id` int NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`name` varchar(64) NOT NULL COMMENT '角色名称',
	`code` varchar(32)  COMMENT '角色编码',
	`sort` int  COMMENT '显示排序',
	`is_enabled` bool  COMMENT '是否可用',
	`remark` varchar(64)  COMMENT '备注',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_code_uk(code),
	INDEX ix_code(code) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户角色';
drop table if exists `app_role_auth`;
create table `app_role_auth` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`role_code` varchar(32) NOT NULL COMMENT '角色编码',
	`auth_type` varchar(16) NOT NULL COMMENT '授权类型{KPI_INDICTATOR(指标),DIM_INDICTATOR(KPI维度指标)}',
	`auth_data` text  COMMENT '授权数据',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	UNIQUE KEY uk_code_uk(role_code),
	INDEX ix_role_code(role_code) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='角色权限';
drop table if exists ` app_account_role`;
create table ` app_account_role` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`code` varchar(32) NOT NULL COMMENT '工号',
	`name` varchar(16) NOT NULL COMMENT '姓名',
	`name_en` varchar(32)  COMMENT '英文名',
	`email` varchar(32)  COMMENT '邮箱',
	`page_role` varchar(32)  COMMENT '页面角色',
	`indicator_role` varchar(32)  COMMENT '指标角色',
	`role_type` varchar(16)  COMMENT '角色类型{store,area}',
	`status` int  COMMENT '状态{1 在职 ,0离职}',
	`insert_time` datetime  COMMENT '数据插入时间',
	`creator` varchar(64) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(64) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_code(code),
	INDEX ix_email(email),
	INDEX ix_insert_time(insert_time) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='人员角色授权';
drop table if exists `app_account_access`;
create table `app_account_access` ( 
	 `id` bigint(20) NOT NULL primary key AUTO_INCREMENT COMMENT 'id',
	`code` int NOT NULL COMMENT '工号',
	`name` varchar(16) NOT NULL COMMENT '姓名',
	`name_en` varchar(32)  COMMENT '英文名',
	`email` varchar(32)  COMMENT '邮箱',
	`role_type` varchar(32)  COMMENT '人员角色',
	`access_type` varchar(32)  COMMENT '组织架构
客户架构',
	`access_lv1` varchar(32)  COMMENT '全国、线上、线下',
	`access_lv2` varchar(32)  COMMENT '大区',
	`access_lv3` varchar(32)  COMMENT '省区',
	`access_lv4` varchar(32)  COMMENT '功能区域',
	`access_lv5` varchar(32)  COMMENT '城市',
	`access_lv6` varchar(32)  COMMENT '门店',
	`creator` varchar(32) NOT NULL COMMENT '行记录创建标记',
	`updater` varchar(32) NOT NULL COMMENT '行记录更新标记',
	`create_time` DATETIME NOT NULL DEFAULT NOW() COMMENT '行记录创建时间',
	`update_time` DATETIME NOT NULL DEFAULT NOW()  ON UPDATE CURRENT_TIMESTAMP COMMENT '行记录更新时间',
	INDEX ix_code_index(code) 
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='人员数据权限';
