INSERT IGNORE INTO kpi_indicator_role_setting (
    kpi_indicator_id,
    role_name,
    setting_code,
    setting_name,
    sort,
    is_enabled,
    `desc`,
    creator,
    updater
)
SELECT
    kirs.kpi_indicator_id,
    kirs.role_name,
    'actual_trend' AS setting_code,  -- 指定新的 setting_code
    '实际值趋势' AS setting_name,     -- 指定新的 setting_name
    4,
    TRUE,
    '实际值趋势',
    '20250317',
    '20250317'
FROM kpi_indicator_role_setting kirs
         INNER JOIN kpi_indicator ki
                    ON kirs.kpi_indicator_id = ki.id   -- 关联指标表
WHERE
    ki.kpi_name LIKE '%库存%' OR ki.kpi_name LIKE '%天数%'          -- 筛选包含[库存]的指标
  AND NOT EXISTS (                   -- 避免重复插入
    SELECT 1
    FROM kpi_indicator_role_setting sub
    WHERE
        sub.kpi_indicator_id = kirs.kpi_indicator_id
      AND sub.setting_code = 'actual_trend'
      AND sub.setting_name = '实际值趋势'
);


SELECT kirs.*
FROM `kpi_indicator_role_setting` kirs
INNER JOIN `kpi_indicator` ki ON kirs.kpi_indicator_id = ki.id
WHERE kirs.setting_code = 'actual_trend'
AND role_name = 'NKA省区/地区/城市渠道负责人'
AND (ki.kpi_name LIKE '%库存%' OR ki.kpi_name LIKE '%天数%');

UPDATE `kpi_indicator_role_setting` kirs
INNER JOIN `kpi_indicator` ki ON kirs.kpi_indicator_id = ki.id
SET kirs.is_enabled = FALSE
WHERE kirs.setting_code = 'actual_trend'
AND role_name = 'NKA省区/地区/城市渠道负责人'
AND (ki.kpi_name LIKE '%库存%' OR ki.kpi_name LIKE '%天数%');

ALTER TABLE kpi_indicator ADD COLUMN ext_desc TEXT;
UPDATE kpi_indicator
SET ext_desc='零售商库存及库存天数的更新频率为月度1次，即每月12号更新上一个月数据' WHERE kpi_name LIKE '%库存%';
UPDATE kpi_indicator
SET ext_desc='零售商库存及库存天数的更新频率为月度1次，即每月12号更新上一个月数据' WHERE kpi_name LIKE '%天数%';
UPDATE kpi_indicator
SET ext_desc='零售商POS更新频率为月度1次，即每月12号更新上一个月数据' WHERE kpi_name LIKE '%POS%';
UPDATE kpi_indicator
SET ext_desc='早阶仓后IMS占比 % = 早阶(S1+S2)产品仓后IMS / 全阶段产品仓后IMS * 100%' WHERE kpi_name LIKE '%早阶仓后IM%';