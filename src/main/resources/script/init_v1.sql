CREATE TABLE dws_slm_shipment_target_product_di (
                                                    id BIGINT AUTO_INCREMENT COMMENT '主键ID',
                                                    stat_date DATE COMMENT '统计日期',
                                                    dealer_code VARCHAR(255) COMMENT '经销商编码',
                                                    geo_code VARCHAR(255) COMMENT 'geo code',
                                                    product_code VARCHAR(255) COMMENT '产品编码',
                                                    shipment_target_dealer_product_krmb_1d DECIMAL(38, 18) COMMENT 'Shipment by 经销商作战单元产品目标金额-千元',
                                                    insert_time DATETIME COMMENT '记录插入时间',
                                                    ds VARCHAR(255) COMMENT 'partition column',
                                                    creator VARCHAR(255) COMMENT '创建人',
                                                    updater VARCHAR(255) COMMENT '更新人',
                                                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                    PRIMARY KEY (id)
) COMMENT='shipment客户产品目标DWS表';

CREATE TABLE dim_product (
                             product_code VARCHAR(255) COMMENT '编码',
                             name VARCHAR(255) COMMENT '名称',
                             name_en VARCHAR(255) COMMENT '名称EN',
                             parent_code VARCHAR(255) COMMENT '父类产品编码',
                             parent_name VARCHAR(255) COMMENT '父类产品名称',
                             parent_name_en VARCHAR(255) COMMENT '父类产品名称EN',
                             type_name VARCHAR(255) COMMENT '类型名称',
                             plant_code VARCHAR(255) COMMENT '工厂编码',
                             plant_name VARCHAR(255) COMMENT '工厂名称',
                             unit VARCHAR(255) COMMENT '单位',
                             weight_unit VARCHAR(255) COMMENT '重量单位',
                             weight_net DECIMAL(38, 18) COMMENT '净重',
                             volume_unit VARCHAR(255) COMMENT '体积单位',
                             volume DECIMAL(38, 18) COMMENT '体积',
                             base_unit VARCHAR(255) COMMENT '基本单位',
                             base_weight_unit VARCHAR(255) COMMENT '基本重量单位',
                             base_weight_net DECIMAL(38, 18) COMMENT '基本净重',
                             base_volume_unit VARCHAR(255) COMMENT '基本体积单位',
                             base_volume DECIMAL(38, 18) COMMENT '基本体积',
                             stage VARCHAR(255) COMMENT '阶段',
                             barcode VARCHAR(255) COMMENT '条码',
                             eachespercs BIGINT COMMENT '箱规',
                             base_price DECIMAL(38, 18) COMMENT '基础价格',
                             brand_code VARCHAR(255) COMMENT '品牌编码',
                             brand_name VARCHAR(255) COMMENT '品牌名称',
                             line_code VARCHAR(255) COMMENT '产品线编码',
                             line_name VARCHAR(255) COMMENT '产品线名称',
                             project_code VARCHAR(255) COMMENT '项目编码',
                             project_name VARCHAR(255) COMMENT '项目名称',
                             reportline_code VARCHAR(255) COMMENT '报告产品线编码',
                             reportline_name VARCHAR(255) COMMENT '报告产品线名称',
                             market_code VARCHAR(255) COMMENT '市场编码',
                             market_name VARCHAR(255) COMMENT '市场名称',
                             category_code VARCHAR(255) COMMENT '品类编码',
                             category_name VARCHAR(255) COMMENT '品类名称',
                             channel_code VARCHAR(255) COMMENT '渠道编码',
                             channel_name VARCHAR(255) COMMENT '渠道名称',
                             isperformcan DECIMAL(38, 18) COMMENT '是否业绩罐',
                             isnproduct VARCHAR(255) COMMENT '是否N产品',
                             issmallcan VARCHAR(255) COMMENT '是否小罐产品',
                             isvalid VARCHAR(255) COMMENT '是否有效',
                             isdelete VARCHAR(255) COMMENT '是否删除',
                             createtime DATE COMMENT '创建时间',
                             etk_id VARCHAR(255) COMMENT 'Etracking id',
                             id VARCHAR(255) COMMENT 'ID',
                             project_name_en VARCHAR(255) COMMENT '',
                             line_name_en VARCHAR(255) COMMENT '',
                             parent_reportline_name VARCHAR(255) COMMENT '',
                             sap_casenetweight DECIMAL(38, 18) COMMENT '',
                             sap_listprice DECIMAL(38, 18) COMMENT '',
                             sap_eachespercs DECIMAL(38, 18) COMMENT '',
                             targetline_name TEXT COMMENT '目标产品线名称',
                             gbtype_name TEXT COMMENT '国标类型',
                             reportline_name_sort INT COMMENT '报告产品线排序',
                             sap_packunitsize VARCHAR(255) COMMENT 'SAP_最小销售单元规格',
                             targetline_name_sort INT COMMENT '报告产品线排序',
                             creator VARCHAR(255) COMMENT '创建人',
                             updater VARCHAR(255) COMMENT '更新人',
                             create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                             PRIMARY KEY (id)
) COMMENT='产品维度表';

CREATE TABLE dim_dealer (
                            id int NOT NULL AUTO_INCREMENT COMMENT 'ID',
                            code VARCHAR(255) COMMENT '编码',
                            name VARCHAR(255) COMMENT '名称',
                            region_name_cn VARCHAR(255) COMMENT '大区名称',
                            region_name_en VARCHAR(255) COMMENT '大区名称EN',
                            subregion_name_cn VARCHAR(255) COMMENT '片区名称',
                            subregion_name_en VARCHAR(255) COMMENT '片区名称EN',
                            area_name_cn VARCHAR(255) COMMENT '区域名称',
                            area_name_en VARCHAR(255) COMMENT '区域名称EN',
                            type_name VARCHAR(255) COMMENT '类型',
                            channel_name VARCHAR(255) COMMENT '渠道名称',
                            class_code VARCHAR(255) COMMENT '分类编码',
                            class_name VARCHAR(255) COMMENT '分类名称',
                            ismindealer VARCHAR(255) COMMENT '',
                            status_code VARCHAR(255) COMMENT '状态编码',
                            status_name VARCHAR(255) COMMENT '状态名称',
                            factoryid INT COMMENT '',
                            reporttype_code VARCHAR(255) COMMENT '报告类型编码',
                            reporttype_name VARCHAR(255) COMMENT '报告类型名称',
                            range_code VARCHAR(30) COMMENT '覆盖区域编码',
                            range_name VARCHAR(100) COMMENT '覆盖区域名称',
                            source VARCHAR(100) COMMENT '1百宝箱 2ETRACKIGN',
                            ds INT COMMENT '',
                            dealer_bu TEXT COMMENT '经销商业务线',
                            baseclass_id TEXT COMMENT '基础经销商类型ID',
                            baseclass_name TEXT COMMENT '基础经销商类型名称',
                            range2_id TEXT COMMENT '经销商报告类型ID',
                            range2_name TEXT COMMENT '经销商报告类型名称',
                            reptype2_id TEXT COMMENT '经销商维度ID',
                            reptype2_name TEXT COMMENT '经销商维度名称',
                            range3_name TEXT COMMENT '二级经销商报告类型名称(日报虚拟维度专用)',
                            range2_name_sort INT COMMENT '经销商报告类型排序',
                            creator VARCHAR(255) COMMENT '创建人',
                            updater VARCHAR(255) COMMENT '更新人',
                            create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            PRIMARY KEY (id)
) COMMENT='经销商维表';

CREATE TABLE dim_store (
                           id int NOT NULL AUTO_INCREMENT COMMENT 'ID',
                           code VARCHAR(128) COMMENT '编码',
                           name VARCHAR(128) COMMENT '名称',
                           parent_code VARCHAR(128) COMMENT '上游门店编码',
                           parent_name VARCHAR(128) COMMENT '上游门店名称',
                           type_code VARCHAR(128) COMMENT '类型编码',
                           type_name VARCHAR(128) COMMENT '类型名称',
                           channel_code VARCHAR(128) COMMENT '渠道编码',
                           channel_name VARCHAR(128) COMMENT '渠道名称',
                           channelcategory_code VARCHAR(128) COMMENT '渠道编码',
                           channelcategory_name VARCHAR(128) COMMENT '渠道名称',
                           mode_code VARCHAR(128) COMMENT '模式编码',
                           mode_name VARCHAR(128) COMMENT '模式名称',
                           dealer_code VARCHAR(128) COMMENT '主经销商编码',
                           dealer_name VARCHAR(128) COMMENT '主经销商名称',
                           dealertype_code VARCHAR(128) COMMENT '著经销商类型编码',
                           delaertype_name VARCHAR(128) COMMENT '著经销商类型名称',
                           chain_code VARCHAR(128) COMMENT '连锁编码',
                           chain_name VARCHAR(128) COMMENT '连锁名称',
                           chaingroup_code VARCHAR(128) COMMENT '连锁群编码',
                           chaingroup_name VARCHAR(128) COMMENT '连锁群名称',
                           chainplatform_code VARCHAR(128) COMMENT '大客户组编码',
                           chainplatform_name VARCHAR(128) COMMENT '大客户组名称',
                           supplieroutlet_code VARCHAR(128) COMMENT '供货模式编码',
                           supplieroutlet_name VARCHAR(128) COMMENT '供货模式名称',
                           province_code VARCHAR(128) COMMENT '省份编码',
                           province_name VARCHAR(128) COMMENT '省份名称',
                           province_name_en VARCHAR(128) COMMENT '省份名称EN',
                           city_code VARCHAR(128) COMMENT '城市编码',
                           city_name VARCHAR(128) COMMENT '城市名称',
                           city_name_en VARCHAR(128) COMMENT '城市名称EN',
                           county_code VARCHAR(128) COMMENT '区县编码',
                           county_name VARCHAR(128) COMMENT '区县名称',
                           county_name_en VARCHAR(128) COMMENT '区县名称EN',
                           street_code VARCHAR(128) COMMENT '街道编码',
                           street_name VARCHAR(128) COMMENT '街道名称',
                           street_name_en VARCHAR(128) COMMENT '街道名称EN',
                           salescity_code VARCHAR(128) COMMENT '销售城市编码',
                           salescity_name VARCHAR(128) COMMENT '销售城市名称',
                           salescity_name_en VARCHAR(128) COMMENT '销售城市名称EN',
                           region_code VARCHAR(128) COMMENT '大区编码',
                           region_name VARCHAR(128) COMMENT '大区名称',
                           region_name_en VARCHAR(128) COMMENT '大区名称EN',
                           subregion_code VARCHAR(128) COMMENT '片区编码',
                           subregion_name VARCHAR(128) COMMENT '片区名称',
                           subregion_name_en VARCHAR(128) COMMENT '片区名称EN',
                           area_code VARCHAR(128) COMMENT '区域编码',
                           area_name VARCHAR(128) COMMENT '区域名称',
                           area_name_en VARCHAR(128) COMMENT '区域名称EN',
                           sheep_dealer_code VARCHAR(128) COMMENT '羊奶经销商编码',
                           sheep_dealer_name VARCHAR(128) COMMENT '羊奶经销商名称',
                           sheep_customertype_code VARCHAR(128) COMMENT '羊奶客户类型编码',
                           sheep_customertype_name VARCHAR(128) COMMENT '羊奶客户类型名称',
                           address VARCHAR(128) COMMENT '详细地址',
                           isncstore VARCHAR(128) COMMENT '是否NC门店',
                           isstrategy VARCHAR(128) COMMENT '是否策略门店',
                           latitude FLOAT COMMENT '经度',
                           longitude FLOAT COMMENT '纬度',
                           sfa_code VARCHAR(128) COMMENT 'SFA编码',
                           status_code VARCHAR(128) COMMENT '状态编码',
                           status_name VARCHAR(128) COMMENT '状态名称',
                           isvalid VARCHAR(128) COMMENT '是否启用',
                           isdelete VARCHAR(128) COMMENT '是否删除',
                           displayorder VARCHAR(128) COMMENT '排序码',
                           closeddate DATETIME COMMENT '关闭日期',
                           createtime DATETIME COMMENT '创建日期',
                           responsible_user_code VARCHAR(128) COMMENT '负责人编码',
                           responsible_user_name VARCHAR(128) COMMENT '负责人名称',
                           property_code VARCHAR(128) COMMENT '性质编码',
                           property_name VARCHAR(128) COMMENT '性质名称',
                           province_id VARCHAR(128) COMMENT '',
                           city_id VARCHAR(128) COMMENT '',
                           country_id VARCHAR(128) COMMENT '',
                           street_id VARCHAR(128) COMMENT '',
                           region_id VARCHAR(128) COMMENT '',
                           subregion_id VARCHAR(128) COMMENT '',
                           area_id VARCHAR(128) COMMENT '',
                           salescity_id VARCHAR(128) COMMENT '',
                           chain_id VARCHAR(128) COMMENT '',
                           chain_name_en VARCHAR(128) COMMENT '',
                           parent_type_name VARCHAR(128) COMMENT '',
                           sales_attribution VARCHAR(128) COMMENT '',
                           chainlevel_name VARCHAR(128) COMMENT '',
                           region_sort INT COMMENT '',
                           channelcategory_name_sort INT COMMENT '',
                           chaingroup_name_sort INT COMMENT '',
                           creator VARCHAR(128) COMMENT '创建人',
                           updater VARCHAR(128) COMMENT '更新人',
                           create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                           PRIMARY KEY (id)
) COMMENT='门店主数据表';

CREATE TABLE dws_slm_pos_target_store_product_di (
                                                     id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                     stat_date VARCHAR(255) COMMENT '统计日期',
                                                     store_code VARCHAR(255) COMMENT '门店code',
                                                     product_code VARCHAR(255) COMMENT '产品code',
                                                     slm_pos_target_cs_1d DECIMAL(38, 18) COMMENT '最近1天_POS目标箱数',
                                                     slm_pos_target_ea_1d DECIMAL(38, 18) COMMENT '最近1天_POS目标罐数',
                                                     slm_pos_target_kg_1d DECIMAL(38, 18) COMMENT '最近1天_POS目标KG数',
                                                     slm_pos_target_amount_asp_krmb_1d DECIMAL(38, 18) COMMENT '最近1天_POS目标ASP千元',
                                                     slm_pos_target_amount_lp_krmb_1d DECIMAL(38, 18) COMMENT '最近1天_POS目标LP千元',
                                                     inserttime DATETIME COMMENT '记录插入时间',
                                                     ds VARCHAR(255) COMMENT 'partition column',
                                                     creator VARCHAR(255) COMMENT '创建人',
                                                     updater VARCHAR(255) COMMENT '更新人',
                                                     create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                     PRIMARY KEY (id)
) COMMENT='POS销售 by 门店(连锁客户) by 产品(报告产品线)';


CREATE TABLE dws_slm_nc_offtake_target_store_producttargetline_di (
                                                                      id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                                      stat_date VARCHAR(255) COMMENT '统计日期',
                                                                      store_code VARCHAR(255) COMMENT '门店编码',
                                                                      geo_code VARCHAR(255) COMMENT '组织架构编码',
                                                                      person_code VARCHAR(255) COMMENT 'NC编码',
                                                                      manager_person_code VARCHAR(255) COMMENT '归属NCL编码',
                                                                      product_code VARCHAR(255) COMMENT '产品编码',
                                                                      slm_nc_offtake_target_store_ea_1d DECIMAL(38, 18) COMMENT '目标罐数',
                                                                      slm_nc_offtake_target_store_lp_krmb_1d DECIMAL(38, 18) COMMENT '目标金额-不含税出厂价',
                                                                      slm_nc_offtake_target_store_rsp_krmb_1d DECIMAL(38, 18) COMMENT '目标金额-建议零售价',
                                                                      slm_nc_offtake_target_store_nua_1d DECIMAL(38, 18) COMMENT 'NUA目标',
                                                                      slm_nc_offtake_target_store_producttargetline_num_1d DECIMAL(38, 18) COMMENT '业绩罐目标',
                                                                      inserttime DATETIME COMMENT '记录插入时间',
                                                                      slm_nc_offtake_target_store_producttargetline_lp_krmb_1d DECIMAL(38, 18) COMMENT '目标金额-不含税出厂价-产品线',
                                                                      ds VARCHAR(255) COMMENT 'partition column',
                                                                      creator VARCHAR(255) COMMENT '创建人',
                                                                      updater VARCHAR(255) COMMENT '更新人',
                                                                      create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                                      update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                                      PRIMARY KEY (id)
) COMMENT='NC Offtake目标DWS表';

CREATE TABLE dws_slm_mbx_kpibystoretodasheng_di (
                                                    id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                    `year_month` BIGINT COMMENT '年份',
                                                    store_code VARCHAR(255) COMMENT '门店代码',
                                                    ims_amont_target DECIMAL(38, 10) COMMENT 'ims门店目标',
                                                    enfa_ims_target DECIMAL(38, 10) COMMENT '调仓后IMS-铂睿门店目标',
                                                    nutripower_ims_target DECIMAL(38, 10) COMMENT '调仓后IMS-儿童线门店目标',
                                                    gentlease_ims_target DECIMAL(38, 10) COMMENT '调仓后IMS-亲舒门店目标',
                                                    enfinitas_ims_target DECIMAL(38, 10) COMMENT '调仓后IMS-大蓝臻门店目标',
                                                    specialty_ims_target DECIMAL(38, 10) COMMENT '调仓后IMS-特配大贸目标',
                                                    ds VARCHAR(255) COMMENT '分区字段',
                                                    creator VARCHAR(255) COMMENT '创建人',
                                                    updater VARCHAR(255) COMMENT '更新人',
                                                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                    PRIMARY KEY (id)
) COMMENT='仓后IMS by 门店 目标表';


CREATE TABLE dws_slm_ims_after_target_storecustomer_productreportline_di (
                                                                             id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                                             stat_date VARCHAR(255) COMMENT '业务日期',
                                                                             store_code VARCHAR(255) COMMENT '门店编码',
                                                                             geo_code VARCHAR(255) COMMENT 'geo_code',
                                                                             product_code VARCHAR(255) COMMENT '产品编码',
                                                                             slm_ims_after_target_storecustomer_productreportline_krmb_1d DECIMAL(38, 18) COMMENT '目标金额-千元',
                                                                             inserttime DATETIME COMMENT '记录插入时间',
                                                                             ds VARCHAR(255) COMMENT '分区字段',
                                                                             creator VARCHAR(255) COMMENT '创建人',
                                                                             updater VARCHAR(255) COMMENT '更新人',
                                                                             create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                                             update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                                             PRIMARY KEY (id)
) COMMENT='仓后IMS by 门店(重点客户) by 产品(报告产品线) 目标表';


CREATE TABLE dws_slm_ims_after_target_storechannel_productreportline_di (
                                                                            id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                                            stat_date VARCHAR(255) COMMENT '业务日期',
                                                                            store_code VARCHAR(255) COMMENT '门店编码',
                                                                            geo_code VARCHAR(255) COMMENT 'geo_code',
                                                                            product_code VARCHAR(255) COMMENT '产品编码',
                                                                            slm_ims_after_target_storechannel_productreportline_krmb_1d DECIMAL(38, 18) COMMENT '目标金额-千元',
                                                                            inserttime DATETIME COMMENT '记录插入时间',
                                                                            ds VARCHAR(255) COMMENT '分区字段',
                                                                            creator VARCHAR(255) COMMENT '创建人',
                                                                            updater VARCHAR(255) COMMENT '更新人',
                                                                            create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                                            update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                                            PRIMARY KEY (id)
) COMMENT='仓后IMS by 门店(渠道) by 产品(报告产品线) 目标表';

CREATE TABLE dws_slm_shipment_target_dealer_di (
                                                   id BIGINT not null AUTO_INCREMENT COMMENT '主键ID',
                                                   stat_date VARCHAR(255) COMMENT '统计日期',
                                                   dealer_code VARCHAR(255) COMMENT '经销商编码',
                                                   geo_code VARCHAR(255) COMMENT 'geo_code',
                                                   shipment_target_dealer_range3_name_krmb_1d DECIMAL(38, 18) COMMENT 'Shipment by 二级经销商报告类型 目标金额-千元',
                                                   shipment_target_dealer_code_krmb_1d DECIMAL(38, 18) COMMENT 'Shipment by 经销商编码 目标金额-千元',
                                                   insert_time DATETIME COMMENT '记录插入时间',
                                                   ds VARCHAR(255) COMMENT '分区字段',
                                                   creator VARCHAR(255) COMMENT '创建人',
                                                   updater VARCHAR(255) COMMENT '更新人',
                                                   create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                   PRIMARY KEY (id)
) COMMENT='shipment客户目标DWS表';

CREATE TABLE ads_slm_mobile_kpi_report_person_m (
                                                    id INT AUTO_INCREMENT NOT NULL COMMENT '主键ID',
                                                    stat_month VARCHAR(255) COMMENT '数据月份',
                                                    stat_quarter VARCHAR(255) COMMENT '数据季度',
                                                    stat_year VARCHAR(255) COMMENT '数据年份',
                                                    person_code VARCHAR(255) COMMENT '工号',
                                                    person_name VARCHAR(255) COMMENT '姓名',
                                                    person_email VARCHAR(255) COMMENT '邮箱',
                                                    person_position VARCHAR(255) COMMENT '岗位',
                                                    person_status VARCHAR(255) COMMENT '在职/离职',
                                                    manager_code VARCHAR(255) COMMENT '直线经理工号',
                                                    manager_name VARCHAR(255) COMMENT '直线经理名称',
                                                    data_type VARCHAR(255) COMMENT '指标类型',
                                                    region_name VARCHAR(255) COMMENT '门店大区',
                                                    subregion_name VARCHAR(255) COMMENT '门店省区',
                                                    area_name VARCHAR(255) COMMENT '门店功能区域',
                                                    channelcategory_name VARCHAR(255) COMMENT '门店渠道',
                                                    mode_name VARCHAR(255) COMMENT '业务单元',
                                                    chain_name VARCHAR(255) COMMENT '连锁客户',
                                                    chaingroup_name VARCHAR(255) COMMENT '全渠道客户组',
                                                    targetline_name VARCHAR(255) COMMENT '目标产品线',
                                                    reportline_name VARCHAR(255) COMMENT '报告产品线',
                                                    stage VARCHAR(255) COMMENT '产品阶段',
                                                    amount_rmb DECIMAL(38, 18) COMMENT '当天金额',
                                                    insert_time DATETIME COMMENT '数据插入时间',
                                                    creator VARCHAR(255) COMMENT '创建者',
                                                    updater VARCHAR(255) COMMENT '更新者',
                                                    create_time DATETIME COMMENT '创建时间',
                                                    update_time DATETIME COMMENT '更新时间',
                                                    PRIMARY KEY (id)
) COMMENT='移动销售KPI报告_月报_一线人员';

CREATE TABLE ads_slm_mobile_kpi_report_area_m (
                                                  id INT AUTO_INCREMENT NOT NULL COMMENT '主键ID',
                                                  stat_month VARCHAR(255) COMMENT '数据月份',
                                                  stat_quarter VARCHAR(255) COMMENT '数据季度',
                                                  stat_year VARCHAR(255) COMMENT '数据年份',
                                                  date_type VARCHAR(255) COMMENT '时间类型 (mtd/qtd/ytd)',
                                                  data_type VARCHAR(255) COMMENT '数据类型 (shipment/ims_after/dealer_inv/nc_offtake/nc_inv/pos_offtake/pos_inv)',
                                                  range2_name VARCHAR(255) COMMENT '经销商报告类型 (shipment/dealer_inv)',
                                                  reptype2_name VARCHAR(255) COMMENT '经销商维度 (shipment/dealer_inv)',
                                                  region_name VARCHAR(255) COMMENT '大区 (共有维度)',
                                                  subregion_name VARCHAR(255) COMMENT '省区 (共有维度)',
                                                  area_name VARCHAR(255) COMMENT '功能区域 (共有维度)',
                                                  channelcategory_name VARCHAR(255) COMMENT '门店渠道 (ims_after/nc_offtake/nc_inv/pos_offtake/pos_inv)',
                                                  mode_name VARCHAR(255) COMMENT '门店业务单元 (ims_after/nc_offtake/nc_inv/pos_offtake/pos_inv)',
                                                  chain_name VARCHAR(255) COMMENT '连锁客户 (ims_after/nc_offtake/nc_inv/pos_offtake/pos_inv)',
                                                  chaingroup_name VARCHAR(255) COMMENT '全渠道客户组 (ims_after/nc_offtake/nc_inv/pos_offtake/pos_inv)',
                                                  targetline_name VARCHAR(255) COMMENT '目标产品线 (共有维度)',
                                                  reportline_name VARCHAR(255) COMMENT '报告产品线 (共有维度)',
                                                  stage VARCHAR(255) COMMENT '产品阶段 (共有维度)',
                                                  qty_days DECIMAL(38,18) COMMENT '当月/当季/当年天数',
                                                  qty_days_p3m DECIMAL(38,18) COMMENT '当月/当季P3M天数',
                                                  qty_days_ly DECIMAL(38,18) COMMENT '去年同月/去年同季/去年同期天数',
                                                  amount_rmb DECIMAL(38,18) COMMENT '当月/当季/当年金额',
                                                  amount_rmb_p3m DECIMAL(38,18) COMMENT '当月/当季P3M金额',
                                                  amount_rmb_ly DECIMAL(38,18) COMMENT '去年同月/去年同期金额',
                                                  amount_rmb_m_doh_fz DECIMAL(38,18) COMMENT '当月库存天数分子金额',
                                                  amount_rmb_m_doh_fm DECIMAL(38,18) COMMENT '当月库存天数分母金额',
                                                  amount_rmb_lm_doh_fz DECIMAL(38,18) COMMENT '上月库存天数分子金额',
                                                  amount_rmb_lm_doh_fm DECIMAL(38,18) COMMENT '上月库存天数分母金额',
                                                  insert_time DATETIME COMMENT '数据插入时间',
                                                  PRIMARY KEY (id)
) COMMENT='移动销售KPI报告_月报_区域';






