alter table app_account_role add column role_cate varchar(24);
ALTER TABLE app_account_role_trans ADD COLUMN role_cate VARCHAR(24);


DROP TABLE IF EXISTS dws_mkt_special_nua_target_di;
DROP TABLE IF EXISTS dws_mkt_special_nua_target_di_trans;
CREATE TABLE `dws_mkt_special_nua_target_di` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `pne_region_name` VARCHAR(16) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(16) COMMENT '医务代表省区',
  `city_name` VARCHAR(16) COMMENT '销售城市',
  `pne_code` VARCHAR(64) COMMENT 'PNE工号',
  `pne_name` VARCHAR(64) COMMENT 'PNE姓名',
  `pne_email` VARCHAR(64) COMMENT 'PNE邮箱',
  `mkt_target_qty_special_nua_hospital_line` BIGINT COMMENT '特配NUA目标数量',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_pne_code` (`pne_code`),
  INDEX `idx_pne_name` (`pne_name`),
  INDEX `idx_pne_email` (`pne_email`)
) ENGINE=INNODB COMMENT='NUA特配目标';

CREATE TABLE `dws_mkt_special_nua_target_di_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `pne_region_name` VARCHAR(16) COMMENT '医务代表大区',
  `pne_subregion_name` VARCHAR(16) COMMENT '医务代表省区',
  `city_name` VARCHAR(16) COMMENT '销售城市',
  `pne_code` VARCHAR(64) COMMENT 'PNE工号',
  `pne_name` VARCHAR(64) COMMENT 'PNE姓名',
  `pne_email` VARCHAR(64) COMMENT 'PNE邮箱',
  `mkt_target_qty_special_nua_hospital_line` BIGINT COMMENT '特配NUA目标数量',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` VARCHAR(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_pne_code` (`pne_code`),
  INDEX `idx_pne_name` (`pne_name`),
  INDEX `idx_pne_email` (`pne_email`)
) ENGINE=INNODB COMMENT='NUA特配目标';


ALTER TABLE ads_slm_mobile_kpi_report_person_m
ADD COLUMN store_code VARCHAR(24),
ADD COLUMN store_name VARCHAR(128);
CREATE INDEX idx_store_code ON ads_slm_mobile_kpi_report_person_m (store_code);
CREATE INDEX idx_store_name ON ads_slm_mobile_kpi_report_person_m (store_name);
ALTER TABLE ads_slm_mobile_kpi_report_person_m_trans
ADD COLUMN store_code VARCHAR(24),
ADD COLUMN store_name VARCHAR(128);
CREATE INDEX idx_store_code ON ads_slm_mobile_kpi_report_person_m_trans (store_code);
CREATE INDEX idx_store_name ON ads_slm_mobile_kpi_report_person_m_trans (store_name);

ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m
ADD COLUMN store_code VARCHAR(24),
ADD COLUMN store_name VARCHAR(128);
CREATE INDEX idx_store_code ON ads_mkt_mobile_kpi_report_person_nua_m (store_code);
CREATE INDEX idx_store_name ON ads_mkt_mobile_kpi_report_person_nua_m (store_name);
ALTER TABLE ads_mkt_mobile_kpi_report_person_nua_m_trans
ADD COLUMN store_code VARCHAR(24),
ADD COLUMN store_name VARCHAR(128);
CREATE INDEX idx_store_code ON ads_mkt_mobile_kpi_report_person_nua_m_trans (store_code);
CREATE INDEX idx_store_name ON ads_mkt_mobile_kpi_report_person_nua_m_trans (store_name);

CREATE TABLE `ads_mkt_mobile_kpi_report_person_act_m` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `person_code` VARCHAR(32) COMMENT '工号',
  `person_name` VARCHAR(64) COMMENT '姓名',
  `person_email` VARCHAR(64) COMMENT '邮箱',
  `person_type` VARCHAR(10) COMMENT '人员类型：营业、PNEP',
  `pne_region_name` VARCHAR(32) COMMENT '医务大区',
  `pne_subregion_name` VARCHAR(32) COMMENT '医务省区',
  `act_report_type_name` VARCHAR(64) COMMENT '活动类目',
  `act_category_name` VARCHAR(64) COMMENT '活动分类名称',
  `category_name` VARCHAR(64) COMMENT '活动大类',
  `type_name` VARCHAR(64) COMMENT '活动类型',
  `region_name` VARCHAR(32) COMMENT '大区',
  `subregion_name` VARCHAR(32) COMMENT '省区',
  `area_name` VARCHAR(32) COMMENT '功能区域',
  `city_name` VARCHAR(32) COMMENT '城市',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `qty_act` BIGINT(20) COMMENT '消费者活动执行场次',
  `qty_act_sign` BIGINT(20) COMMENT '消费者活动到会家庭数',
  `qty_onsite_buy_first` BIGINT(20) COMMENT '活动现场首购新用户数量',
  `qty_act_ly` BIGINT(20) COMMENT '去年同期 - 消费者活动场次',
  `qty_act_sign_ly` BIGINT(20) COMMENT '去年同期 - 消费者活动到会家庭数',
  `qty_onsite_buy_first_ly` BIGINT(20) COMMENT '去年同期 - 活动现场首购新用户数量',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_person_code` (`person_code`),
  INDEX `idx_person_name` (`person_name`),
  INDEX `idx_person_type` (`person_type`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_act_report_type_name` (`act_report_type_name`),
  INDEX `idx_act_category_name` (`act_category_name`),
  INDEX `idx_category_name` (`category_name`),
  INDEX `idx_type_name` (`type_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`)
);

CREATE TABLE `ads_mkt_mobile_kpi_report_person_act_m_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `person_code` VARCHAR(32) COMMENT '工号',
  `person_name` VARCHAR(64) COMMENT '姓名',
  `person_email` VARCHAR(64) COMMENT '邮箱',
  `person_type` VARCHAR(10) COMMENT '人员类型：营业、PNEP',
  `pne_region_name` VARCHAR(32) COMMENT '医务大区',
  `pne_subregion_name` VARCHAR(32) COMMENT '医务省区',
  `act_report_type_name` VARCHAR(64) COMMENT '活动类目',
  `act_category_name` VARCHAR(64) COMMENT '活动分类名称',
  `category_name` VARCHAR(64) COMMENT '活动大类',
  `type_name` VARCHAR(64) COMMENT '活动类型',
  `region_name` VARCHAR(32) COMMENT '大区',
  `subregion_name` VARCHAR(32) COMMENT '省区',
  `area_name` VARCHAR(32) COMMENT '功能区域',
  `city_name` VARCHAR(32) COMMENT '城市',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `qty_act` BIGINT(20) COMMENT '消费者活动执行场次',
  `qty_act_sign` BIGINT(20) COMMENT '消费者活动到会家庭数',
  `qty_onsite_buy_first` BIGINT(20) COMMENT '活动现场首购新用户数量',
  `qty_act_ly` BIGINT(20) COMMENT '去年同期 - 消费者活动场次',
  `qty_act_sign_ly` BIGINT(20) COMMENT '去年同期 - 消费者活动到会家庭数',
  `qty_onsite_buy_first_ly` BIGINT(20) COMMENT '去年同期 - 活动现场首购新用户数量',
  `insert_time` DATETIME COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_person_code` (`person_code`),
  INDEX `idx_person_name` (`person_name`),
  INDEX `idx_person_type` (`person_type`),
  INDEX `idx_pne_region_name` (`pne_region_name`),
  INDEX `idx_pne_subregion_name` (`pne_subregion_name`),
  INDEX `idx_act_report_type_name` (`act_report_type_name`),
  INDEX `idx_act_category_name` (`act_category_name`),
  INDEX `idx_category_name` (`category_name`),
  INDEX `idx_type_name` (`type_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`),
  INDEX `idx_city_name` (`city_name`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`)
);



CREATE TABLE `ads_mkt_mobile_kpi_report_sales_store_m` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `region_name` VARCHAR(16) COMMENT '大区',
  `subregion_name` VARCHAR(16) COMMENT '省区',
  `area_name` VARCHAR(16) COMMENT '功能区域',
  `active_store_tgt_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当月',
  `active_store_act_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当月',
  `active_store_tgt_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当季',
  `active_store_act_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当季',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`)
) ENGINE=InnoDB COMMENT='移动业绩KPI报告_月报_活跃门店指标';
CREATE TABLE `ads_mkt_mobile_kpi_report_sales_store_m_trans` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `stat_month` VARCHAR(12) COMMENT '数据月份',
  `stat_quarter` VARCHAR(12) COMMENT '数据季度',
  `stat_year` VARCHAR(6) COMMENT '数据年份',
  `channelcategory_name` VARCHAR(32) COMMENT '门店渠道',
  `region_name` VARCHAR(16) COMMENT '大区',
  `subregion_name` VARCHAR(16) COMMENT '省区',
  `area_name` VARCHAR(16) COMMENT '功能区域',
  `active_store_tgt_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当月',
  `active_store_act_m` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当月',
  `active_store_tgt_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店目标数-当季',
  `active_store_act_q` DECIMAL(38, 18) COMMENT '销售执行 - 月度活跃门店实际数-当季',
  `insert_time` TIMESTAMP NULL COMMENT '数据插入时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `creator` varchar(32) DEFAULT NULL COMMENT '创建人',
  `updater` VARCHAR(32) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  INDEX `idx_stat_month` (`stat_month`),
  INDEX `idx_stat_quarter` (`stat_quarter`),
  INDEX `idx_stat_year` (`stat_year`),
  INDEX `idx_channelcategory_name` (`channelcategory_name`),
  INDEX `idx_region_name` (`region_name`),
  INDEX `idx_subregion_name` (`subregion_name`),
  INDEX `idx_area_name` (`area_name`)
) ENGINE=InnoDB COMMENT='移动业绩KPI报告_月报_活跃门店指标';


--  "act_report_type_name" text COLLATE "pg_catalog"."default",
--   "pne_region_name" text COLLATE "pg_catalog"."default",
--   "pne_subregion_name" text COLLATE "pg_catalog"."default",
--   "person_code" text COLLATE "pg_catalog"."default",
--   "person_type" text COLLATE "pg_catalog"."default",
--   "person_email" text COLLATE "pg_catalog"."default"
alter table dws_actv_activity_target_di
add column act_report_type_name varchar(32),
add column pne_region_name varchar(24),
add column pne_subregion_name varchar(24),
add column person_code varchar(32),
add column person_type varchar(10),
add column person_email varchar(64);

alter table dws_actv_activity_target_di_trans
add column act_report_type_name varchar(32),
add column pne_region_name varchar(24),
add column pne_subregion_name varchar(24),
add column person_code varchar(32),
add column person_type varchar(10),
add column person_email varchar(64);

-- 创建索引
create index idx_act_report_type_name on dws_actv_activity_target_di(act_report_type_name);
create index idx_pne_region_name on dws_actv_activity_target_di(pne_region_name);
create index idx_pne_subregion_name on dws_actv_activity_target_di(pne_subregion_name);
create index idx_person_email on dws_actv_activity_target_di(person_email);

create index idx_act_report_type_name on dws_actv_activity_target_di_trans(act_report_type_name);
create index idx_pne_region_name on dws_actv_activity_target_di_trans(pne_region_name);
create index idx_pne_subregion_name on dws_actv_activity_target_di_trans(pne_subregion_name);
create index idx_person_email on dws_actv_activity_target_di_trans(person_email);


--   "act_report_type_name" text COLLATE "pg_catalog"."default",
--   "pne_region_name" text COLLATE "pg_catalog"."default",
--   "pne_subregion_name" text COLLATE "pg_catalog"."default",
--   "person_type" text COLLATE "pg_catalog"."default"

alter table ads_mkt_mobile_kpi_report_area_act_m
add column act_report_type_name varchar(32),
add column pne_region_name varchar(24),
add column pne_subregion_name varchar(24),
add column person_type varchar(10);
alter table ads_mkt_mobile_kpi_report_area_act_m_trans
add column act_report_type_name varchar(32),
add column pne_region_name varchar(24),
add column pne_subregion_name varchar(24),
add column person_type varchar(10);

-- 创建索引
create index idx_act_report_type_name on ads_mkt_mobile_kpi_report_area_act_m(act_report_type_name);
create index idx_pne_region_name on ads_mkt_mobile_kpi_report_area_act_m(pne_region_name);
create index idx_pne_subregion_name on ads_mkt_mobile_kpi_report_area_act_m(pne_subregion_name);
create index idx_person_type on ads_mkt_mobile_kpi_report_area_act_m(person_type);  

create index idx_act_report_type_name on ads_mkt_mobile_kpi_report_area_act_m_trans(act_report_type_name);
create index idx_pne_region_name on ads_mkt_mobile_kpi_report_area_act_m_trans(pne_region_name);
create index idx_pne_subregion_name on ads_mkt_mobile_kpi_report_area_act_m_trans(pne_subregion_name);
create index idx_person_type on ads_mkt_mobile_kpi_report_area_act_m_trans(person_type);







