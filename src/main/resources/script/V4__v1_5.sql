UPDATE `kpi_indicator_detail` SET CODE = kpi_code WHERE NAME IN ('活动执行场次','活动到会家庭数','活动现场Offtake','现场首购新用户数');

INSERT INTO `kpi_sql_template` (id, `template_handler`, `area_sql_tmpl`, `store_sql_tmpl`, `is_enabled`, `remark`, `creator`, `updater`)
VALUES
    (19, 'ACT_QUERY', 'SELECT actual / NULLIF(target, 0) AS comp, actual, target, _yoy FROM (SELECT SUM(qty_act) AS actual, IF(SUM(qty_act_ly) <= 0, NULL, SUM(qty_act) / NULLIF(SUM(qty_act_ly), 0) - 1) AS _yoy FROM ads_mkt_mobile_kpi_report_area_act_m WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' ) ) AS actual_query, (SELECT COALESCE(SUM(actv_target_qty_act_m), 0) AS target FROM dws_actv_activity_target_di WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' )) AS target_query', NULL, TRUE, 'CS活动执行场次', 'manual', 'manual'),


    (21, 'ACT_QUERY', 'SELECT actual / NULLIF(target, 0) AS comp, actual, target, _yoy FROM (SELECT SUM(qty_act) AS actual, IF(SUM(qty_act_ly) <= 0, NULL, SUM(qty_act) / NULLIF(SUM(qty_act_ly), 0) - 1) AS _yoy FROM ads_mkt_mobile_kpi_report_area_act_m WHERE act_category_name = \'医务\') AS actual_query, (SELECT COALESCE(SUM(actv_target_qty_act_m), 0) AS target FROM dws_actv_activity_target_di WHERE act_category_name = \'医务\') AS target_query', NULL, TRUE, '消费者教育活动执行场次', 'manual', 'manual'),


    (20, 'ACT_QUERY', 'SELECT actual / NULLIF(target, 0) AS comp, actual, target, _yoy FROM (SELECT SUM(qty_act_sign) AS actual, IF(SUM(qty_act_ly) <= 0, NULL, SUM(qty_act_sign) / NULLIF(SUM(qty_act_sign_ly), 0) - 1) AS _yoy FROM ads_mkt_mobile_kpi_report_area_act_m WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' )) AS actual_query, (SELECT COALESCE(SUM(actv_target_qty_act_sign_m), 0) AS target FROM dws_actv_activity_target_di WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' )) AS target_query', NULL, TRUE, 'CS活动到会家庭数', 'manual', 'manual'),


    (22, 'ACT_QUERY', 'SELECT actual / NULLIF(target, 0) AS comp, actual, target, _yoy FROM (SELECT SUM(qty_act_sign) AS actual, IF(SUM(qty_act_ly) <= 0, NULL, SUM(qty_act_sign) / NULLIF(SUM(qty_act_sign_ly), 0) - 1) AS _yoy FROM ads_mkt_mobile_kpi_report_area_act_m WHERE act_category_name = \'医务\') AS actual_query, (SELECT COALESCE(SUM(actv_target_qty_act_sign_m), 0) AS target FROM dws_actv_activity_target_di WHERE act_category_name = \'医务\') AS target_query', NULL, TRUE, '消费者教育活动到会家庭数', 'manual', 'manual');


update `kpi_sql_template`
set area_sql_tmpl = '	SELECT
	actual / NULLIF ( target, 0 ) AS comp ,actual,target,_yoy
FROM
	(
	SELECT COALESCE (SUM(amount_rmb_onsite_offtake),0) AS actual,
	SUM( amount_rmb_onsite_offtake ) / NULLIF( SUM( amount_rmb_onsite_offtake_ly ), 0 ) - 1 AS _yoy
	FROM
		ads_mkt_mobile_kpi_report_area_act_m
		WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' )

	) AS actual_query,
	(
	SELECT SUM(actv_target_amount_rmb_onsite_offtake_m) AS target
	FROM
		dws_actv_activity_target_di WHERE act_category_name = \'营业\' OR ( act_category_name=\'营业顾问\' AND  type_name!=\'迷你活动\' )
	) AS target_query' where id = 13;

INSERT INTO `kpi_indicator_detail` (
    `name`,
    `code`,
    `kpi_code`,
    `sql_template_id`,
    `is_mtd_support`,
    `is_qtd_support`,
    `is_ytd_support`,
    `is_personal_support`,
    `is_enabled`,
    `creator`,
    `updater`
)
VALUES
    ('CS活动执行场次', 'qty_cs_act', 'qty_cs_act', 19, 1, 1, 1, 1, 1, 'system', 'system'),
    ('CS活动到会家庭数', 'qty_cs_act_sign', 'qty_cs_act_sign', 20, 1, 1, 1, 1, 1, 'system', 'system'),
    ('消费者教育活动执行场次', 'qty_edu_act', 'qty_edu_act', 21, 1, 1, 1, 1, 1, 'system', 'system'),
    ('消费者教育活动到会家庭数', 'qty_edu_act_sign', 'qty_edu_act_sign', 22, 1, 1, 1, 1, 1, 'system', 'system');

UPDATE kpi_indicator_detail SET NAME='CS活动现场Offtake' WHERE NAME = '活动现场Offtake';
UPDATE kpi_indicator_detail SET NAME='院外消费者教育活动-现场首购新用户数' WHERE NAME = '现场首购新用户数';

INSERT INTO `kpi_indicator` (
    `kpi_name`,
    `code`,
    `group_id`,
    `category_id`,
    `unit`,
    `context`,
    `desc`,
    `is_mtd_support`,
    `is_qtd_support`,
    `is_ytd_support`,
    `sort`,
    `is_enabled`,
    `creator`,
    `updater`
)
VALUES
    ('CS活动执行场次', 'qty_cs_act', 2, 3, '场', 1, NULL, 1, 1, 1, 17, 1, 1, 1),
    ('消费者教育活动执行场次', 'qty_edu_act', 2, 3, '场', 1, NULL, 1, 1, 1, 18, 1, 1, 1),
    ('CS活动到会家庭数', 'qty_cs_act_sign', 2, 3, '个', 1, NULL, 1, 1, 1, 19, 1, 1, 1),
    ('消费者教育活动到会家庭数', 'qty_edu_act_sign', 2, 3, '个', 1, NULL, 1, 1, 1, 20, 1, 1, 1);

UPDATE kpi_indicator SET kpi_name='CS活动现场Offtake', sort=21 WHERE kpi_name = '活动现场Offtake';
UPDATE kpi_indicator SET kpi_name='院外消费者教育活动-现场首购新用户数', sort=22 WHERE kpi_name = '现场首购新用户数';

UPDATE app_account_role SET indicator_name='CS活动现场Offtake' WHERE indicator_name = '活动现场Offtake';
UPDATE app_account_role SET indicator_name='院外消费者教育活动-现场首购新用户数' WHERE indicator_name = '现场首购新用户数';


DELETE FROM kpi_indicator WHERE kpi_name IN ('活动执行场次','活动到会家庭数');
DELETE FROM kpi_indicator_detail WHERE NAME IN ('活动执行场次','活动到会家庭数');

--以下未必需要执行，得等卡片表更新
INSERT INTO `app_account_role` (
    `role_type`,
    `role_name`,
    `indicator_type`,
    `indicator_name`,
    `card_name`,
    `filter_1`,
    `filter_2`,
    `filter_3`,
    `filter_4`,
    `filter_5`,
    `filter_6`,
    `remark`,
    `insert_time`,
    `creator`,
    `updater`
)
SELECT
    role_type,
    role_name,
    indicator_type,
    CASE
        WHEN indicator_name = '活动执行场次' THEN 'CS活动执行场次'
        WHEN indicator_name = '活动到会家庭数' THEN 'CS活动到会家庭数'
        END,
    card_name,
    filter_1,
    filter_2,
    filter_3,
    filter_4,
    filter_5,
    filter_6,
    remark,
    insert_time,
    creator,
    updater
FROM app_account_role
WHERE indicator_name IN ('活动执行场次','活动到会家庭数')

UNION ALL

SELECT
    role_type,
    role_name,
    indicator_type,
    CASE
        WHEN indicator_name = '活动执行场次' THEN '消费者教育活动执行场次'
        WHEN indicator_name = '活动到会家庭数' THEN '消费者教育活动到会家庭数'
        END,
    card_name,
    filter_1,
    filter_2,
    filter_3,
    filter_4,
    filter_5,
    filter_6,
    remark,
    insert_time,
    creator,
    updater
FROM app_account_role
WHERE indicator_name IN ('活动执行场次','活动到会家庭数');


DELETE FROM app_account_role WHERE indicator_name IN ('活动执行场次','活动到会家庭数');
