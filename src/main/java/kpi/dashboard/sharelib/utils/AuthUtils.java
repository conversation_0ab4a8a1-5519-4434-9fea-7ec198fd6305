package kpi.dashboard.sharelib.utils;

import org.springframework.security.core.context.SecurityContextHolder;
import kpi.dashboard.sharelib.domain.bo.AuthUserInfo;

public class AuthUtils {
    public static AuthUserInfo getUserInfo(){
                try {
                    if (SecurityContextHolder.getContext().getAuthentication().getPrincipal().equals("anonymousUser")) {
                        AuthUserInfo authUserInfo = AuthUserInfo.builder().identifier("<EMAIL>").build();
                        return authUserInfo;
                    }
                    AuthUserInfo authUserInfo = (AuthUserInfo)SecurityContextHolder.getContext().getAuthentication().getDetails();
                    //todo: enhance by yourself
        //        Object[] grantedAuthorities = SecurityContextHolder.getContext().getAuthentication().getAuthorities().toArray();
        //        for (Object auths : grantedAuthorities) {
        //            String authValue = String.valueOf(auths);
        //            if (authValue.contains(ADMIN_CHANNEL)) {
        //                String[] data = authValue.split(AUTH_DATA_SPLITTER);
        //                authUserInfo.getAuthDatas().add(new AuthUserInfo.AuthDataDetail(data[1], data[2]));
        //            }
        //        }
                    return authUserInfo;
                }catch (Exception e){
                    return null;
                }
    }
}
