package kpi.dashboard.sharelib.utils.log;

import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface ApiLog {

    boolean isEnable() default true;
    boolean printInputs() default true;
    boolean printOutputs() default true;
    boolean storage() default false;
    String storageDescription() default "";
}
