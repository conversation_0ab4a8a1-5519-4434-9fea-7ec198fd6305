package kpi.dashboard.sharelib.utils.log;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.core.NamedThreadLocal;
import org.springframework.stereotype.Component;
import kpi.dashboard.sharelib.domain.db.mapper.ApiCallLogMapper;

import javax.annotation.Resource;

@Slf4j
@Aspect
@Component
public class APILogComponent {

    @Resource
    private ApiCallLogMapper apiCallLogMapper;

    private static final ThreadLocal<Long> beginTimeThreadLocal = new NamedThreadLocal<>("Log controller start time");

    public APILogComponent(){
        log.info("Enabling APILogComponent");
    }


    @Before(value = "execution(public * *(..)) && @annotation(ApiLog)")
    public void beforMethod(JoinPoint point) {
        beginTimeThreadLocal.set(System.currentTimeMillis());
        new GenericApiAccessLogExecutor(point).printBeforeLog();
    }

    @AfterReturning(value = "execution(public * *(..)) && @annotation(ApiLog)", returning = "result")
    public Object afterReturning(JoinPoint jp, Object result) {
        new GenericApiAccessLogExecutor(jp, apiCallLogMapper)
            .printAfterLog(result, beginTimeThreadLocal.get());
        beginTimeThreadLocal.remove();
        return result;
    }

    @AfterThrowing(value = "execution(public * *(..)) && @annotation(ApiLog)", throwing = "e")
    public void exceptionOccured(JoinPoint jp, Throwable e) {
        new GenericApiAccessLogExecutor(jp, apiCallLogMapper)
            .printExceptionLog(e, beginTimeThreadLocal.get());
        beginTimeThreadLocal.remove();
    }


}
