package kpi.dashboard.sharelib.utils.log;


import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import kpi.dashboard.sharelib.domain.bo.AuthUserInfo;
import kpi.dashboard.sharelib.domain.db.mapper.ApiCallLogMapper;
import kpi.dashboard.sharelib.domain.db.model.ApiCallLog;
import kpi.dashboard.sharelib.domain.dto.InternalMessage;
import kpi.dashboard.sharelib.domain.dto.InternalResponse;
import kpi.dashboard.sharelib.errors.InternalException;
import kpi.dashboard.sharelib.utils.AuthUtils;
import kpi.dashboard.sharelib.utils.IPUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GenericApiAccessLogExecutor implements ApiAccessLogExecutor {

    protected static Map<String, Entry> cacheData = new HashMap<>();
    protected final JoinPoint joinPoint;
    protected Entry entry;
    protected String beforeLogTemplate = "Start to execute method=【%s】 with ip=【%s】, params=【%s】";
    protected String beforeLogTemplateWithUser = "Start to execute method=【%s】 with user=【%s】,ip=【%s】,params=【%s】";
    protected String afterLogTemplate = "End to excecute method=【%s】 with exectime=【%d】,ip=【%s】, result=【%s】";
    protected String afterLogTemplateWithUser = "End to excecute method=【%s】 with exectime=【%d】,user=【%s】,ip=【%s】," +
        "result=【%s】";
    //protected String exceptionTemplate = "Error occurred while excecute method=【%s】";
    private ApiCallLogMapper apiCallLogMapper;
    private static String appName;

    static {
        try {
            Properties props = new Properties();
            InputStream inputStream =
                (Thread.currentThread().getContextClassLoader().getResource("bootstrap.yml").openStream());
            props.load(inputStream);
            appName = props.getProperty("name");
        } catch (IOException e) {
            log.error("Error loading app.properties file");
            System.exit(-1);
        }
    }

    public GenericApiAccessLogExecutor(JoinPoint joinPoint) {
        this.joinPoint = joinPoint;
        buildEntry(joinPoint);
    }

    public GenericApiAccessLogExecutor(JoinPoint joinPoint, ApiCallLogMapper apiCallLogMapper) {
        this.apiCallLogMapper = apiCallLogMapper;
        this.joinPoint = joinPoint;
        buildEntry(joinPoint);
    }

    @Override
    public void printBeforeLog() {
        if (getIsEnable(true)) {
            String params = getParams();
            AuthUserInfo userInfo = AuthUtils.getUserInfo();
            String beforeLog = userInfo == null ?
                String.format(beforeLogTemplate, entry.getRequestPath(), getIp(), params) :
                String.format(beforeLogTemplateWithUser, entry.getRequestPath(), userInfo.getIdentifier(), getIp(),
                    params);
            log.info(beforeLog);
        }
    }

    @Override
    public void printAfterLog(Object object, long execStartTime) {
        Long exeTime = System.currentTimeMillis() - execStartTime;
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        if (getIsEnable(false)) {
            String beforeLog = userInfo == null ?
                String.format(afterLogTemplate, entry.getRequestPath(), exeTime, getIp(), getParamAsStr(object)) :
                String.format(afterLogTemplateWithUser, entry.getRequestPath(), exeTime, userInfo.getIdentifier(),
                    getIp(), getParamAsStr(object));
            log.info(beforeLog);
        }
        if (entry.getStorage()) {
            try {
                recordApiLog(userInfo, object, exeTime);
            } catch (Exception e) {
                log.error("持久化日志切面时出现异常", e);
            }
        }
    }

    @Override
    public void printExceptionLog(Throwable e, long execStartTime) {
        AuthUserInfo userInfo = AuthUtils.getUserInfo();
        Long exeTime = System.currentTimeMillis() - execStartTime;
        if (userInfo == null) {
            log.error("Runtime exception occurred when excecute method=【{}】 with exectime=【{}}】, params=【{}】",
                entry.getRequestPath(), exeTime, getParams());
        } else {
            log.error("Runtime exception occurred when excecute method=【{}】 with user=【{}】, exectime=【{}}】, " +
                    "params=【{}】",
                entry.getRequestPath(),
                userInfo.getIdentifier(),
                exeTime,
                getParams());
        }
        if (entry.getStorage()) {
            try {
                recordApiLog(userInfo, e, exeTime);
            } catch (Exception exception) {
                log.error("持久化日志切面时出现异常", exception);
            }
        }
    }


    private String getIp() {
        try {
            RequestAttributes ra = RequestContextHolder.getRequestAttributes();
            ServletRequestAttributes sra = (ServletRequestAttributes) ra;
            HttpServletRequest request = sra.getRequest();
            return IPUtils.getClinetIpByReq(request);
        } catch (Exception e) {
            return null;
        }
    }

    private String getUrl() {
        try {
            RequestAttributes ra = RequestContextHolder.getRequestAttributes();
            ServletRequestAttributes sra = (ServletRequestAttributes) ra;
            return sra.getRequest().getRequestURL().toString();
        } catch (Exception e) {
            return null;
        }
    }

    private String getParamAsStr(Object parameterValue) {
        if (null == parameterValue) {
            return "null";
        }
        return new Gson().toJson(parameterValue);
    }

    private String getMethodName(String fieldName) {
        char[] chars = fieldName.toCharArray();
        chars[0] = toUpperCase(chars[0]);
        return "get" + String.valueOf(chars);
    }

    private char toUpperCase(char c) {
        if (97 <= c && c <= 122) {
            c ^= 32;
        }
        return c;
    }

    @Transactional(rollbackFor = Exception.class)
    public void recordApiLog(AuthUserInfo userInfo, Object object, Long exeTime) {
        if (apiCallLogMapper != null) {
            ApiCallLog apiCallLog = new ApiCallLog();
            apiCallLog.setDescription(entry.getDescription());
            apiCallLog.setIp(getIp());
            apiCallLog.setUrl(getUrl());
            apiCallLog.setMethod(entry.getId());
            apiCallLog.setUserId(userInfo == null ? "-1" : userInfo.getIdentifier());
            apiCallLog.setReqBody(getRequestBody());
            apiCallLog.setReqParam(getRequestParams());
            if(object instanceof InternalException){
                InternalException internalException = (InternalException)object;
                apiCallLog.setRespCode(internalException.getErrorCode());
                apiCallLog.setRespMessage(internalException.getCustomizeMessage());
            }else {
                apiCallLog.setRespCode(InternalResponse.success().getCode());
                apiCallLog.setRespMessage(InternalResponse.success().getMessage());
            }
            if (appName == null) {
                appName = "NoSetting";
            }
            apiCallLog.setCreator(appName);
            apiCallLog.setUpdater(appName);
            patchResponseBodyOrExceptionMessage(object, apiCallLog);
            apiCallLog.setExectime(exeTime == null ? null : String.valueOf(exeTime));
            apiCallLogMapper.insert(apiCallLog);
        }
    }

    protected String getRequestBody() {
        JsonObject jsonObject = new JsonObject();
        Class<?>[] parameterTypes = entry.getMethod().getParameterTypes();//参数类型
        Parameter[] parameters = entry.getMethod().getParameters();
        Object[] args = joinPoint.getArgs();//参数值
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            if (parameter.isAnnotationPresent(RequestBody.class) || parameter.isAnnotationPresent(RequestPart.class)) {
                Class<?> parameterClass = parameterTypes[i];
                Object parameterValue = args[i];
                if (parameterClass.isPrimitive()) {
                    JsonObject innerObject = new JsonObject();
                    innerObject.addProperty(parameter.getName(), parameterClass.toString());
                    jsonObject.add(parameter.getName(), innerObject);
                } else if (buildSkipParameterPrintOut().stream().anyMatch(parameterClass::isAssignableFrom)) {
                    JsonObject innerObject = new JsonObject();
                    innerObject.addProperty(parameter.getName(), parameterClass.toString());
                    jsonObject.add(parameter.getName(), innerObject);
                } else {
                    jsonObject.add(parameter.getName(), new Gson().toJsonTree(parameterValue));
                }
            }
        }
        return jsonObject.size() == 0 ? null : jsonObject.toString();
    }

    protected String getRequestParams() {
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        HttpServletRequest request = sra.getRequest();
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (!parameterMap.isEmpty()) {
            return new Gson().toJson(parameterMap);
        }
        return null;
    }

        protected List<Class<?>> buildSkipParameterPrintOut() {
            List<Class<?>> skipClassList = new LinkedList<>();
            try {
                Class<?> httpServletResponse = Class.forName("javax.servlet.http.HttpServletResponse");
                skipClassList.add(httpServletResponse);
            } catch (ClassNotFoundException e) {
                log.warn("class javax.servlet.http.HttpServletResponse not found.", e);
            }
            try {
                Class<?> httpServletRequest = Class.forName("javax.servlet.http.HttpServletRequest");
                skipClassList.add(httpServletRequest);
            } catch (ClassNotFoundException e) {
                log.warn("class javax.servlet.http.HttpServletRequest not found.", e);
            }
            return skipClassList;
        }

        private boolean getIsEnable(Boolean checkStart) {
            if(checkStart) {
                return (!ObjectUtils.isEmpty(entry) && entry.getIsEnable() && entry.getIsStartLogEnabled());
            }
            return (!ObjectUtils.isEmpty(entry) && entry.getIsEnable() && entry.getIsEndLogEnabled());
        }

        private void buildEntry(JoinPoint joinPoint) {
            Class<?> clazz = joinPoint.getTarget().getClass();
            List<Method> methods = Arrays.stream(clazz.getMethods()).filter(item -> item.getName().equals(joinPoint.getSignature().getName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(methods)) {
                return;
            }

            Method method = methods.get(0);
            String entryId = String.format("%s.%s", clazz.getName(), method.getName());//ex:cs.naturade.share.lib.Test.function
            if (cacheData.containsKey(entryId)) {
                this.entry = cacheData.get(entryId);
            } else {
                this.entry = new Entry(clazz, method);
                cacheData.put(entryId, entry);
            }
        }

        protected String getParams() {
            Parameter[] parameters = entry.getMethod().getParameters();
            Class<?>[] parameterTypes = entry.getMethod().getParameterTypes();//参数类型
            Object[] args = joinPoint.getArgs();//参数值
            String[] parameterStr = new String[args.length];//存放格式化之后的json
            for (int i = 0; i < parameters.length; i++) {
                Parameter parameter = parameters[i];
                Class<?> parameterClass = parameterTypes[i];
                Object parameterValue = args[i];

                //区分原始类型和封装类型
                if (parameterClass.isPrimitive()) {
                    parameterStr[i] = (String.format("\"%s\":\"%s\"", parameter.getName(), parameterValue));
                } else if (buildSkipParameterPrintOut().stream().anyMatch(parameterClass::isAssignableFrom)) {
                    parameterStr[i] = (String.format("\"%s\":\"%s\"", parameter.getName(), parameterValue));
                } else {
                    parameterStr[i] = (String.format("\"%s\":%s", parameter.getName(), getParamAsStr(parameterValue)));
                }
            }
            return StringUtils.join(parameterStr, ",");
        }

    private void patchResponseBodyOrExceptionMessage(Object object, ApiCallLog apiCallLog) {
        if (object instanceof InternalResponse) {
            apiCallLog.setRespCode(getResultParam(object, "code", false));
            apiCallLog.setRespMessage(getResultParam(object, "message", false));
            apiCallLog.setRespBody(getResultParam(object, "body", true));
        }
        if (object instanceof InternalException) {
            apiCallLog.setRespCode((((InternalException) object).getErrorCode()));
            String message = InternalMessage.getMessage(appName, (((InternalException) object).getErrorCode()));
            if (message != null && message.contains("%s")) {
                if (((InternalException) object).getParams() != null && ((InternalException) object).getParams()[0] != null) {
                    message = String.format(message, ((InternalException) object).getParams()[0]);
                }
            }
            initExceptionMessage(object, apiCallLog);
            apiCallLog.setRespMessage(message);
        } else if (object instanceof Exception) {
            InternalResponse<?> internalResponse = InternalResponse.fail();
            apiCallLog.setRespCode(internalResponse.getCode());
            initExceptionMessage(object, apiCallLog);
            apiCallLog.setRespMessage(internalResponse.getMessage());
        }
    }

    private String getResultParam(Object result, String paramName, boolean toJson) {
        try {
            if (result != null) {
                if (result instanceof InternalResponse) {
                    Class<?> clazz = result.getClass();
                    Method method = clazz.getMethod(getMethodName(paramName));
                    Object o = method.invoke(result);
                    if (o == null) {
                        return null;
                    }
                    return toJson ? new Gson().toJson(o) : o.toString();
                }
            }
        } catch (Exception e) {
            log.error("日志切面反射获取InternalResponse时出现错误", e);
        }
        return null;
    }

    private void initExceptionMessage(Object object, ApiCallLog apiCallLog) {
        Map<String, JsonElement> map = new HashMap<>(2);
        map.put("stackTrace", new Gson().toJsonTree(((Exception) object).getStackTrace()));
        map.put("class", new Gson().toJsonTree(object.getClass().toString()));
        apiCallLog.setExceptionMessage(new Gson().toJson(map));
    }

        @Data
        public static class Entry {
            protected Class<?> clazz;
            protected Method method;
            private String id;
            private Boolean isEnable;
            private Boolean storage;
            private String requestPath;
            private String description;

            public Entry(Class<?> clazz, Method method) {
                this.id = String.format("%s.%s", clazz.getName(), method.getName());//ex:cs.naturade.share.lib.Test.function
                this.clazz = clazz;
                this.method = method;
                this.storage = getStorage();
                this.isEnable = getIsEnable();
                this.requestPath = getPath();
                this.description = getDescription();
            }

            private Boolean getIsEnable() {
                boolean flag = false;
                //先检查方��的该注解
                if (method.isAnnotationPresent(ApiLog.class)) {
                    ApiLog annotation = method.getAnnotation(ApiLog.class);
                    flag = annotation.isEnable();
                } else {
                    //再检查类上面的该注解
                    if (clazz.isAnnotationPresent(ApiLog.class)) {
                        Annotation annotation = clazz.getAnnotation(ApiLog.class);
                        try {
                            flag = (boolean) annotation.getClass().getDeclaredMethod("isEnable").invoke(annotation);
                        } catch (Exception e) {
                            log.error("解析ApiLog属性isEnable失败，", e);
                        }
                    }
                }
                return flag;
            }

            private Boolean getIsStartLogEnabled() {
                boolean flag = false;
                //先检查方��的该注解
                if (method.isAnnotationPresent(ApiLog.class)) {
                    ApiLog annotation = method.getAnnotation(ApiLog.class);
                    flag = annotation.printInputs();
                } else {
                    //再检查类上面的该注解
                    if (clazz.isAnnotationPresent(ApiLog.class)) {
                        Annotation annotation = clazz.getAnnotation(ApiLog.class);
                        try {
                            flag = (boolean) annotation.getClass().getDeclaredMethod("isEnable").invoke(annotation);
                        } catch (Exception e) {
                            log.error("解析ApiLog属性isEnable失败，", e);
                        }
                    }
                }
                return flag;
            }

            private Boolean getIsEndLogEnabled() {
                boolean flag = false;
                //先检查方��的该注解
                if (method.isAnnotationPresent(ApiLog.class)) {
                    ApiLog annotation = method.getAnnotation(ApiLog.class);
                    flag = annotation.printOutputs();
                } else {
                    //再检查类上面的该注解
                    if (clazz.isAnnotationPresent(ApiLog.class)) {
                        Annotation annotation = clazz.getAnnotation(ApiLog.class);
                        try {
                            flag = (boolean) annotation.getClass().getDeclaredMethod("isEnable").invoke(annotation);
                        } catch (Exception e) {
                            log.error("解析ApiLog属性isEnable失败，", e);
                        }
                    }
                }
                return flag;
            }

            private Boolean getStorage() {
                boolean flag = false;
                //先检查方��的该注解
                if (method.isAnnotationPresent(ApiLog.class)) {
                    ApiLog annotation = method.getAnnotation(ApiLog.class);
                    flag = annotation.storage();
                } else {
                    //再检查类上面的该注解
                    if (clazz.isAnnotationPresent(ApiLog.class)) {
                        Annotation annotation = clazz.getAnnotation(ApiLog.class);
                        try {
                            flag = (boolean) annotation.getClass().getDeclaredMethod("storage").invoke(annotation);
                        } catch (Exception e) {
                            log.error("解析ApiLog属性storage失败，", e);
                        }
                    }
                }
                return flag;
            }

            private String getPath() {
                String classMapping = "";
                String methodMapping = "";
                try {
                    if (clazz.isAnnotationPresent(RequestMapping.class)) {
                        Annotation annotation = clazz.getAnnotation(RequestMapping.class);
                        String[] path = (String[]) annotation.getClass().getDeclaredMethod("value").invoke(annotation);
                        classMapping = fixMappingPath(classMapping, path);
                    }

                    if (this.method.isAnnotationPresent(RequestMapping.class)) {
                        String[] path = this.method.getAnnotation(RequestMapping.class).value();
                        methodMapping = fixMappingPath(methodMapping, path);
                    } else {
                        Annotation[] annotations = this.method.getAnnotations();
                        for (Annotation annotation : annotations) {
                            if (annotation.annotationType().isAnnotationPresent(RequestMapping.class)) {
                                String[] path = (String[]) annotation.getClass().getDeclaredMethod("value").invoke(annotation);
                                methodMapping = fixMappingPath(methodMapping, path);
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("解析RequestPath失败，", e);
                }
                return classMapping + methodMapping;
            }

            //把地址修正为统一的格式，以/开头不以/结尾。ex：/xx
            private String fixMappingPath(String classMapping, String[] path) {
                if (ArrayUtils.isNotEmpty(path)) {
                    classMapping = path[0];
                    if (StringUtils.isNotEmpty(classMapping)) {
                        if (!StringUtils.startsWith(classMapping, "/")) {
                            classMapping = String.format("/%s", classMapping);
                        }

                        if (StringUtils.endsWith(classMapping, "/")) {
                            classMapping = classMapping.substring(0, classMapping.length() - 1);
                        }
                    }
                }
                return classMapping;
            }
        private String getDescription() {
            String flag = null;
            //先检查方��的该注解
            if (method.isAnnotationPresent(ApiLog.class)) {
                ApiLog annotation = method.getAnnotation(ApiLog.class);
                flag = annotation.storageDescription();
            } else {
                //再检查类上面的该注解
                if (clazz.isAnnotationPresent(ApiLog.class)) {
                    Annotation annotation = clazz.getAnnotation(ApiLog.class);
                    try {
                        flag =
                            (String) annotation.getClass().getDeclaredMethod("storageDescription").invoke(annotation);
                    } catch (Exception e) {
                        log.error("解析ApiLog属性storageDescription失败，", e);
                    }
                }
            }
            return StringUtils.isEmpty(flag) ? null : flag;
        }
    }
}
