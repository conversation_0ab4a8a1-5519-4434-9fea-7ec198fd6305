package kpi.dashboard.sharelib.domain.db.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import kpi.dashboard.sharelib.common.MainBaseAdminEntity;

import java.util.Date;
import lombok.Data;
import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysRoleAccount extends MainBaseAdminEntity implements Serializable{
    @TableId(type = IdType.AUTO)
	private Integer id;
	private Integer roleId;
	private Integer accountId;

}
