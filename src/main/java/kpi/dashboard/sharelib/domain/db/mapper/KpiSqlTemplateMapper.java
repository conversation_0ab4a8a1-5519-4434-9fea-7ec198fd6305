package kpi.dashboard.sharelib.domain.db.mapper;


import java.util.Date;

import kpi.dashboard.sharelib.domain.db.model.KpiSqlTemplate;
import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

@Mapper
public interface KpiSqlTemplateMapper extends BaseMapper<KpiSqlTemplate> {

     @Insert("INSERT IGNORE INTO kpi_sql_template(id,template_handler,area_sql_tmpl,store_sql_tmpl,extras,is_enabled,remark,creator,updater)" +
                " VALUES(#{id},#{templateHandler},#{areaSqlTmpl},#{storeSqlTmpl},#{extras},#{isEnabled},#{remark},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(KpiSqlTemplate kpiSqlTemplate);

    @Insert("INSERT INTO kpi_sql_template(id,template_handler,area_sql_tmpl,store_sql_tmpl,extras,is_enabled,remark,creator,updater)" +
                " VALUES(#{id},#{templateHandler},#{areaSqlTmpl},#{storeSqlTmpl},#{extras},#{isEnabled},#{remark},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE template_handler=VALUES(template_handler),area_sql_tmpl=VALUES(area_sql_tmpl),store_sql_tmpl=VALUES(store_sql_tmpl),extras=VALUES(extras),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(KpiSqlTemplate kpiSqlTemplate);

    @Update("UPDATE kpi_sql_template set template_handler=#{templateHandler},area_sql_tmpl=#{areaSqlTmpl},store_sql_tmpl=#{storeSqlTmpl},extras=#{extras},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(KpiSqlTemplate kpiSqlTemplate);

    @Delete("DELETE FROM kpi_sql_template WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_sql_template set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,template_handler,area_sql_tmpl,store_sql_tmpl,extras,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_sql_template WHERE id=#{id} ")
    @Results(id = "kpiSqlTemplate-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "templateHandler", column = "template_handler"),
      @Result(property = "areaSqlTmpl", column = "area_sql_tmpl"),
      @Result(property = "storeSqlTmpl", column = "store_sql_tmpl"),
      @Result(property = "extras", column = "extras"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    KpiSqlTemplate getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,template_handler,area_sql_tmpl,store_sql_tmpl,extras,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_sql_template WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiSqlTemplate-mapping")
    KpiSqlTemplate getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


//update status sqls
    @Update("update kpi_sql_template set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );




//get data by foreign keys

}
