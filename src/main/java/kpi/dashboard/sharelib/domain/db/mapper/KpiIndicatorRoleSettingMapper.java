package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiIndicatorRoleSetting;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface KpiIndicatorRoleSettingMapper extends BaseMapper<KpiIndicatorRoleSetting> {

    /**
     * 添加
     *
     * @param kpiIndicatorRoleSetting KpiIndicatorRoleSetting
     * @return 条数
     */
    @Insert("INSERT IGNORE INTO kpi_indicator_role_setting(id,kpi_indicator_id,role_name,setting_code,setting_name,sort,is_enabled,`desc`,creator,updater)" +
            " VALUES(#{id},#{kpiIndicatorId},#{roleName},#{settingCode},#{settingName},#{sort},#{isEnabled},#{desc},#{sort},#{isEnabled},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiIndicatorRoleSetting kpiIndicatorRoleSetting);

    /**
     * 批量插入 KPI 指标角色设置数据（使用 @Insert 注解）
     *
     * @param kpiIndicatorRoleSettings 待插入的实体列表
     * @return 影响的行数
     */
    @Insert("<script>" +
            "INSERT IGNORE INTO kpi_indicator_role_setting (" +
            "id, kpi_indicator_id, role_name, setting_code, setting_name, sort, is_enabled, `desc`, creator, updater" +
            ") VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.kpiIndicatorId}, #{item.roleName}, #{item.settingCode}, #{item.settingName}, " +
            "#{item.sort}, #{item.isEnabled}, #{item.desc}, #{item.creator}, #{item.updater})" +
            "</foreach>" +
            "</script>")
    @Options(useGeneratedKeys = false)
    int batchInsertIgnoreEntities(@Param("list") List<KpiIndicatorRoleSetting> kpiIndicatorRoleSettings);

    /**
     * 根据角色面呈获取设置数据
     *
     * @param roleName 角色名称
     * @return List<KpiIndicatorRoleSetting>
     */
    @Select("SELECT id, kpi_indicator_id, role_name, setting_code, setting_name, sort, is_enabled, `desc`, creator, updater,create_time,update_time FROM kpi_indicator_role_setting WHERE is_enabled = true AND role_name = #{roleName}")
    @Results(id = "kpi_indicator_role_setting-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "kpiIndicatorId", column = "kpi_indicator_id"),
            @Result(property = "roleName", column = "role_name"),
            @Result(property = "settingCode", column = "setting_code"),
            @Result(property = "settingName", column = "setting_name"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "desc", column = "desc"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    List<KpiIndicatorRoleSetting> getKpiIndicatorRoleSettingList(@Param("roleName") String roleName);


    /**
     * 根据角色名称 指标id获取配置数据
     *
     * @param roleName 角色名称
     * @param code     指标code
     * @return KpiIndicatorRoleSetting
     */
    @Select("SELECT " +
            "t1.* " +
            "FROM " +
            "kpi_indicator_role_setting t1 " +
            "JOIN kpi_indicator t2 ON t1.kpi_indicator_id = t2.id AND t2.is_enabled " +
            "WHERE t1.is_enabled = true AND t1.role_name = #{roleName} AND t2.code = #{code} ORDER BY t1.sort")
    @ResultMap(value = "kpi_indicator_role_setting-mapping")
    List<KpiIndicatorRoleSetting> getSettingListByRoleNameAndKpiIndicatorId(@Param("roleName") String roleName, @Param("code") String code);
}
