package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class KpiIndicator extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String kpiName;
    private String code;
    private Integer groupId;
    private Integer categoryId;
    private String desc;
    private String unit;
    private String context;
    private Integer sort;
    private String extDesc;

    private Boolean isEnabled;

}
