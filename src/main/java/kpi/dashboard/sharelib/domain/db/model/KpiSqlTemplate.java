package kpi.dashboard.sharelib.domain.db.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;

import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.util.Date;
import lombok.Data;
import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class KpiSqlTemplate extends MainBaseAdminEntity implements Serializable{
    @TableId(type = IdType.AUTO)
	private Integer id;
	private String templateHandler;
	private String areaSqlTmpl;
	private String storeSqlTmpl;
	private String extras;
	private Boolean isEnabled;
	private String remark;

}
