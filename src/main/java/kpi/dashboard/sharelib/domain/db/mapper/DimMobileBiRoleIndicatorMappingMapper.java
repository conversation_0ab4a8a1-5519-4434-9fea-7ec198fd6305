package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.DimMobileBiRoleIndicatorMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DimMobileBiRoleIndicatorMappingMapper extends BaseMapper<DimMobileBiRoleIndicatorMapping> {

    /**
     * 获取DimMobileBiRoleIndicatorMapping数据
     *
     * @param roleName         roleName
     * @param data             data
     * @param indicatorDimName indicatorDimName
     * @return List<DimMobileBiRoleIndicatorMapping>
     */
    @Select("select * from dim_mobile_bi_role_indicator_mapping where role_name = #{roleName} and indicator_dim_name = #{indicatorDimName} and start_month<= #{data} and end_month >=#{data} order by indicator_value asc")
    List<DimMobileBiRoleIndicatorMapping> selectByRoleNameAndDate(@Param("roleName") String roleName, @Param("indicatorDimName") String indicatorDimName, @Param("data") String data);

}
