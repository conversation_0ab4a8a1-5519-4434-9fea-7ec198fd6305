package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class KpiDataSyncTask extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private String id;
    private String taskName;
    private String sourceTableName;
    private String targetTableName;
    private Boolean isSuc;
    private Date dataTime;
    private Date jobStartTime;
    private Date jobEndTime;
    private Boolean hasFinished;
    private Integer sucCount;
    private Integer failedCount;

}
