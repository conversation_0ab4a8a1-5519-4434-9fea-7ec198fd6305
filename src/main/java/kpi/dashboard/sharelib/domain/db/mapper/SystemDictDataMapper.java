package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.SystemDictData;
import org.apache.ibatis.annotations.*;

@Mapper
public interface SystemDictDataMapper extends BaseMapper<SystemDictData> {

    @Insert("INSERT IGNORE INTO system_dict_data(id,label,value,dict_type_id,sort,remark,creator,updater)" +
            " VALUES(#{id},#{label},#{value},#{dictTypeId},#{sort},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(SystemDictData systemDictData);

    @Insert("INSERT INTO system_dict_data(id,label,value,dict_type_id,sort,remark,creator,updater)" +
            " VALUES(#{id},#{label},#{value},#{dictTypeId},#{sort},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE label=VALUES(label),value=VALUES(value),dict_type_id=VALUES(dict_type_id),sort=VALUES(sort),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(SystemDictData systemDictData);

    @Update("UPDATE system_dict_data set label=#{label},value=#{value},dict_type_id=#{dictTypeId},sort=#{sort},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(SystemDictData systemDictData);

    @Delete("DELETE FROM system_dict_data WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update system_dict_data set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,label,value,dict_type_id,sort,remark,creator,updater,create_time,update_time FROM system_dict_data WHERE id=#{id} ")
    @Results(id = "systemDictData-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "label", column = "label"),
            @Result(property = "value", column = "value"),
            @Result(property = "dictTypeId", column = "dict_type_id"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    SystemDictData getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,label,value,dict_type_id,sort,remark,creator,updater,create_time,update_time FROM system_dict_data WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "systemDictData-mapping")
    SystemDictData getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
