package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiTab;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface KpiTabMapper extends BaseMapper<KpiTab> {

    @Insert("INSERT IGNORE INTO kpi_tab(id,tab_name,tab_code,kpi_indicator_id,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{tabName},#{tabCode},#{kpiIndicatorId},#{sort},#{isEnabled},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiTab kpiTab);

    @Insert("INSERT INTO kpi_tab(id,tab_name,tab_code,kpi_indicator_id,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{tabName},#{tabCode},#{kpiIndicatorId},#{sort},#{isEnabled},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE tab_name=VALUES(tab_name),tab_code=VALUES(tab_code),kpi_indicator_id=VALUES(kpi_indicator_id),sort=VALUES(sort),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiTab kpiTab);

    @Update("UPDATE kpi_tab set tab_name=#{tabName},tab_code=#{tabCode},kpi_indicator_id=#{kpiIndicatorId},sort=#{sort},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiTab kpiTab);

    @Delete("DELETE FROM kpi_tab WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_tab set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,tab_name,tab_code,kpi_indicator_id,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_tab WHERE id=#{id} ")
    @Results(id = "kpiTab-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "tabName", column = "tab_name"),
            @Result(property = "tabCode", column = "tab_code"),
            @Result(property = "kpiIndicatorId", column = "kpi_indicator_id"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiTab getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,tab_name,tab_code,kpi_indicator_id,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_tab WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiTab-mapping")
    KpiTab getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_tab set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);

    @Select("SELECT id,tab_name,tab_code,kpi_indicator_id,sort,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_tab WHERE kpi_indicator_id=#{indicatorId} and is_enabled=true ORDER BY sort ASC")
    List<KpiTab> getKpiTabByKpiIndicatorId(@Param("indicatorId")Integer indicatorId);


//get data by foreign keys

}
