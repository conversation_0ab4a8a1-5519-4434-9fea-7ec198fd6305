package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.AppRoleAuth;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AppRoleAuthMapper extends BaseMapper<AppRoleAuth> {

    @Insert("INSERT IGNORE INTO app_role_auth(id,role_code,auth_type,auth_data,creator,updater)" +
            " VALUES(#{id},#{roleCode},#{authType},#{authData},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(AppRoleAuth appRoleAuth);

    @Insert("INSERT INTO app_role_auth(id,role_code,auth_type,auth_data,creator,updater)" +
            " VALUES(#{id},#{roleCode},#{authType},#{authData},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE auth_type=VALUES(auth_type),auth_data=VALUES(auth_data),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(AppRoleAuth appRoleAuth);

    @Update("UPDATE app_role_auth set role_code=#{roleCode},auth_type=#{authType},auth_data=#{authData},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(AppRoleAuth appRoleAuth);

    @Delete("DELETE FROM app_role_auth WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update app_role_auth set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,role_code,auth_type,auth_data,creator,updater,create_time,update_time FROM app_role_auth WHERE id=#{id} ")
    @Results(id = "appRoleAuth-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "roleCode", column = "role_code"),
            @Result(property = "authType", column = "auth_type"),
            @Result(property = "authData", column = "auth_data"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    AppRoleAuth getByIdEX(@Param("id") Long id);

    @Select("SELECT id,role_code,auth_type,auth_data,creator,updater,create_time,update_time FROM app_role_auth WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "appRoleAuth-mapping")
    AppRoleAuth getByIdFilterIsDeleted(@Param("id") Long id);

    //get data by unique keys
    @Select("SELECT id,role_code,auth_type,auth_data,creator,updater,create_time,update_time FROM app_role_auth WHERE role_code=#{roleCode} ")
    @ResultMap(value = "appRoleAuth-mapping")
    AppRoleAuth getByRoleCode(@Param("roleCode") String roleCode);


//update status sqls


//get data by foreign keys

}
