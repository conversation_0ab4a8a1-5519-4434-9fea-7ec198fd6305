package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiIndicator;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface KpiIndicatorMapper extends BaseMapper<KpiIndicator> {

    @Insert("INSERT IGNORE INTO kpi_indicator(id,kpi_name,code,group_id,category_id,desc,unit,context,sort,is_enabled,creator,updater)" +
            " VALUES(#{id},#{kpiName},#{code},#{groupId},#{categoryId},#{desc},#{unit},#{context},#{sort},#{isEnabled},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiIndicator kpiIndicator);

    @Insert("INSERT INTO kpi_indicator(id,kpi_name,code,group_id,category_id,desc,unit,context,sort,is_enabled,creator,updater)" +
            " VALUES(#{id},#{kpiName},#{code},#{groupId},#{categoryId},#{desc},#{sort},#{unit},#{context},#{isEnabled},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE kpi_name=VALUES(kpi_name),code=VALUES(code),group_id=VALUES(group_id),category_id=VALUES(category_id),desc=VALUES(desc),unit=VALUES(unit),context=VALUES(context),sort=VALUES(sort),is_enabled=VALUES(is_enabled),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiIndicator kpiIndicator);

    @Update("UPDATE kpi_indicator set kpi_name=#{kpiName},code=#{code},group_id=#{groupId},category_id=#{categoryId},desc=#{desc},unit=#{unit},context=#{context},sort=#{sort},is_enabled=#{isEnabled},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiIndicator kpiIndicator);

    @Delete("DELETE FROM kpi_indicator WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_indicator set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,kpi_name,code,group_id,category_id,desc,unit,context,sort,is_enabled,creator,updater,create_time,update_time FROM kpi_indicator WHERE id=#{id} ")
    @Results(id = "kpiIndicator-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "kpiName", column = "kpi_name"),
            @Result(property = "code", column = "code"),
            @Result(property = "groupId", column = "group_id"),
            @Result(property = "categoryId", column = "category_id"),
            @Result(property = "desc", column = "desc"),
            @Result(property = "unit", column = "unit"),
            @Result(property = "context", column = "context"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiIndicator getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,kpi_name,code,group_id,category_id,desc,unit,context,sort,is_enabled,creator,updater,create_time,update_time FROM kpi_indicator WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiIndicator-mapping")
    KpiIndicator getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_indicator set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


    @Select({
            "<script>",
            "SELECT id,kpi_name,code,group_id,category_id FROM kpi_indicator",
            "WHERE is_enabled = true AND code IN",
            "<foreach item='item' index='index' collection='codes' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>" +
            "ORDER BY sort ASC",
            "</script>"
    })
    List<KpiIndicator> findByCodes(@Param("codes") List<String> codes);

    @Select("SELECT id,kpi_name,code,group_id,category_id,`desc`,unit,context,sort,is_enabled,creator,updater,create_time,update_time FROM kpi_indicator WHERE is_enabled = true ORDER BY sort ASC")
    List<KpiIndicator> getAll();


//get data by foreign keys

}
