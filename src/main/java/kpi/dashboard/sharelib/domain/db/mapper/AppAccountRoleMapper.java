package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.AppAccountRole;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AppAccountRoleMapper extends Base<PERSON>apper<AppAccountRole> {

    @Insert("INSERT IGNORE INTO  app_account_role(id,role_type,role_name,indicator_type,indicator_name,card_name,filter_1,filter_2,filter_3,filter_4,filter_5,filter_6,remark,insert_time,creator,updater)" +
            " VALUES(#{id},#{roleType},#{roleName},#{indicatorType},#{indicatorName},#{cardName},#{filter1},#{filter2},#{filter3},#{filter4},#{filter5},#{filter6},#{remark},#{insertTime},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(AppAccountRole appAccountRole);

    @Insert("INSERT INTO  app_account_role(id,role_type,role_name,indicator_type,indicator_name,card_name,filter_1,filter_2,filter_3,filter_4,filter_5,filter_6,remark,insert_time,creator,updater)" +
            " VALUES(#{id},#{roleType},#{roleName},#{indicatorType},#{indicatorName},#{cardName},#{filter1},#{filter2},#{filter3},#{filter4},#{filter5},#{filter6},#{remark},#{insertTime},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE role_type=VALUES(role_type),role_name=VALUES(role_name),indicator_type=VALUES(indicator_type),indicator_name=VALUES(indicator_name),card_name=VALUES(card_name),filter_1=VALUES(filter_1),filter_2=VALUES(filter_2),filter_3=VALUES(filter_3),filter_4=VALUES(filter_4),filter_5=VALUES(filter_5),filter_6=VALUES(filter_6),remark=VALUES(remark),insert_time=VALUES(insert_time),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(AppAccountRole appAccountRole);

    @Update("UPDATE  app_account_role set role_type=#{roleType},role_name=#{roleName},indicator_type=#{indicatorType},indicator_name=#{indicatorName},card_name=#{cardName},filter_1=#{filter1},filter_2=#{filter2},filter_3=#{filter3},filter_4=#{filter4},filter_5=#{filter5},filter_6=#{filter6},remark=#{remark},insert_time=#{insertTime},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(AppAccountRole appAccountRole);

    @Delete("DELETE FROM  app_account_role WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update  app_account_role set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,role_type,role_name,indicator_type,indicator_name,card_name,filter_1,filter_2,filter_3,filter_4,filter_5,filter_6,remark,insert_time,creator,updater,create_time,update_time FROM  app_account_role WHERE id=#{id} ")
    @Results(id = " appAccountRole-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "roleType", column = "role_type"),
            @Result(property = "roleName", column = "role_name"),
            @Result(property = "indicatorType", column = "indicator_type"),
            @Result(property = "indicatorName", column = "indicator_name"),
            @Result(property = "cardName", column = "card_name"),
            @Result(property = "filter1", column = "filter_1"),
            @Result(property = "filter2", column = "filter_2"),
            @Result(property = "filter3", column = "filter_3"),
            @Result(property = "filter4", column = "filter_4"),
            @Result(property = "filter5", column = "filter_5"),
            @Result(property = "filter6", column = "filter_6"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "insertTime", column = "insert_time"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    AppAccountRole getByIdEX(@Param("id") Long id);

    @Select("SELECT id,role_type,role_name,indicator_type,indicator_name,card_name,filter_1,filter_2,filter_3,filter_4,filter_5,filter_6,remark,insert_time,creator,updater,create_time,update_time FROM  app_account_role WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = " appAccountRole-mapping")
    AppAccountRole getByIdFilterIsDeleted(@Param("id") Long id);

    @Select("SELECT id,code,`name`,name_en,email,page_role,indicator_role,role_type,status,insert_time,creator,updater,create_time,update_time FROM  app_account_role WHERE email=#{email} ")
    @ResultMap(value = " appAccountRole-mapping")
    AppAccountRole getByEmail(@Param("email") String email);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
