package kpi.dashboard.sharelib.domain.db.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppAccountAccess extends MainBaseAdminEntity implements Serializable{
    @TableId(type = IdType.AUTO)
	private Long id;
	private String statMonth;
	private String userCode;
	private String userName;
	private String userNameEn;
	private String userEmail;
	private String roleType;
	private String roleName;
	private String accessType;
    private String level1;
    private String level2;
    private String level3;
    private String level4;
    private String level5;
    private String level6;
    private String source;
    private String remark;
    private Date insertTime;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppAccountAccess that = (AppAccountAccess) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(statMonth, that.statMonth) &&
                Objects.equals(userCode, that.userCode) &&
                Objects.equals(userName, that.userName) &&
                Objects.equals(userNameEn, that.userNameEn) &&
                Objects.equals(userEmail, that.userEmail) &&
                Objects.equals(roleType, that.roleType) &&
                Objects.equals(roleName, that.roleName) &&
                Objects.equals(accessType, that.accessType) &&
                Objects.equals(level1, that.level1) &&
                Objects.equals(level2, that.level2) &&
                Objects.equals(level3, that.level3) &&
                Objects.equals(level4, that.level4) &&
                Objects.equals(level5, that.level5) &&
                Objects.equals(level6, that.level6);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, statMonth, userCode, userName, userNameEn, userEmail, roleType, roleName, accessType, level1, level2, level3, level4, level5, level6);
    }

}
