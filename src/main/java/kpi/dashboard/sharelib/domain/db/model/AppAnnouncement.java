package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppAnnouncement extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    @TableField("id")
    private Integer id;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    @TableField("announcement")
    private String announcement;

    @TableField("is_enabled")
    private Boolean isEnabled;

    @TableField("remark")
    private String remark;

}
