package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.SysRoleAuth;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SysRoleAuthMapper extends BaseMapper<SysRoleAuth> {

     @Insert("INSERT IGNORE INTO sys_role_auth(id,auth_type,channel,authed_data,role_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{authType},#{channel},#{authedData},#{roleId},#{createdByUser},#{updatedByUser},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(SysRoleAuth sysRoleAuth);

    @Insert("INSERT INTO sys_role_auth(id,auth_type,channel,authed_data,role_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{authType},#{channel},#{authedData},#{roleId},#{createdByUser},#{updatedByUser},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE authed_data=VALUES(authed_data),updated_by_user=VALUES(updated_by_user),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(SysRoleAuth sysRoleAuth);

    @Update("UPDATE sys_role_auth set auth_type=#{authType},channel=#{channel},authed_data=#{authedData},role_id=#{roleId},updated_by_user=#{updatedByUser},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(SysRoleAuth sysRoleAuth);

    @Delete("DELETE FROM sys_role_auth WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update sys_role_auth set is_deleted=true, updated_by_user=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,auth_type,channel,authed_data,role_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_auth WHERE id=#{id} ")
    @Results(id = "sysRoleAuth-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "authType", column = "auth_type"),
      @Result(property = "channel", column = "channel"),
      @Result(property = "authedData", column = "authed_data"),
      @Result(property = "roleId", column = "role_id"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    SysRoleAuth getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,auth_type,channel,authed_data,role_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_auth WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "sysRoleAuth-mapping")
    SysRoleAuth getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,auth_type,channel,authed_data,role_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_auth WHERE auth_type=#{authType} and channel=#{channel} and role_id=#{roleId} ")
    @ResultMap(value = "sysRoleAuth-mapping")
    SysRoleAuth getByAuthTypeChannelRoleId(@Param("authType") String authType,@Param("channel") String channel,@Param("roleId") Integer roleId);



//update status sqls


//get data by foreign keys

}
