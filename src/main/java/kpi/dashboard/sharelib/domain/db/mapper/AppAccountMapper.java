package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.AppAccount;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AppAccountMapper extends BaseMapper<AppAccount> {

    @Insert("INSERT IGNORE INTO app_account(id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater)" +
            " VALUES(#{id},#{userid},#{code},#{name},#{nameEn},#{gender},#{avatar},#{qrCode},#{mobile},#{regTime},#{regChannel},#{email},#{bizMail},#{address},#{isEnabled},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(AppAccount appAccount);

    @Insert("INSERT INTO app_account(id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater)" +
            " VALUES(#{id},#{userid},#{code},#{name},#{nameEn},#{gender},#{avatar},#{qrCode},#{mobile},#{regTime},#{regChannel},#{email},#{bizMail},#{address},#{isEnabled},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE code=VALUES(code),`name`=VALUES(`name`),name_en=VALUES(name_en),gender=VALUES(gender),avatar=VALUES(avatar),qr_code=VALUES(qr_code),mobile=VALUES(mobile),reg_time=VALUES(reg_time),reg_channel=VALUES(reg_channel),biz_mail=VALUES(biz_mail),address=VALUES(address),is_enabled=VALUES(is_enabled),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(AppAccount appAccount);

    @Update("UPDATE app_account set userid=#{userid},code=#{code},name=#{name},name_en=#{nameEn},gender=#{gender},avatar=#{avatar},qr_code=#{qrCode},mobile=#{mobile},reg_time=#{regTime},reg_channel=#{regChannel},email=#{email},biz_mail=#{bizMail},address=#{address},is_enabled=#{isEnabled},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(AppAccount appAccount);

    @Delete("DELETE FROM app_account WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update app_account set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE id=#{id} ")
    @Results(id = "appAccount-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "userid", column = "userid"),
            @Result(property = "code", column = "code"),
            @Result(property = "name", column = "name"),
            @Result(property = "nameEn", column = "name_en"),
            @Result(property = "gender", column = "gender"),
            @Result(property = "avatar", column = "avatar"),
            @Result(property = "qrCode", column = "qr_code"),
            @Result(property = "mobile", column = "mobile"),
            @Result(property = "regTime", column = "reg_time"),
            @Result(property = "regChannel", column = "reg_channel"),
            @Result(property = "email", column = "email"),
            @Result(property = "bizMail", column = "biz_mail"),
            @Result(property = "address", column = "address"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    AppAccount getByIdEX(@Param("id") Long id);

    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "appAccount-mapping")
    AppAccount getByIdFilterIsDeleted(@Param("id") Long id);

    //get data by unique keys
    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE userid=#{userid} ")
    @ResultMap(value = "appAccount-mapping")
    AppAccount getByUserid(@Param("userid") String userid);

    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE userid=#{userid} and is_enabled = true ")
    @ResultMap(value = "appAccount-mapping")
    AppAccount getValidByUserid(@Param("userid") String userid);

    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE email=#{email} ")
    @ResultMap(value = "appAccount-mapping")
    AppAccount getByEmail(@Param("email") String email);

    @Select("SELECT id,userid,code,`name`,name_en,gender,avatar,qr_code,mobile,reg_time,reg_channel,email,biz_mail,address,is_enabled,creator,updater,create_time,update_time FROM app_account WHERE email=#{email} and is_enabled = true ")
    @ResultMap(value = "appAccount-mapping")
    AppAccount getValidByEmail(@Param("email") String email);


    //update status sqls
    @Update("update app_account set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
