package kpi.dashboard.sharelib.domain.db.model;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppRoleAuth extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String roleCode;
    private String authType;
    private String authData;

}
