package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SystemDictData extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String label;
    private String value;
    private Integer dictTypeId;
    private Integer sort;
    private String remark;

}
