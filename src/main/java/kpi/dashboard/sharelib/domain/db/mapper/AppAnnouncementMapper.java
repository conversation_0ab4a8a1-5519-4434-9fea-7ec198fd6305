package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.AppAnnouncement;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AppAnnouncementMapper extends Base<PERSON>apper<AppAnnouncement> {

    @Insert("INSERT IGNORE INTO app_announcement(id,start_time,end_time,announcement,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{startTime},#{endTime},#{announcement},#{isEnabled},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(AppAnnouncement appAnnouncement);

    @Insert("INSERT INTO app_announcement(id,start_time,end_time,announcement,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{startTime},#{endTime},#{announcement},#{isEnabled},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE start_time=VALUES(start_time),end_time=VALUES(end_time),announcement=VALUES(announcement),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(AppAnnouncement appAnnouncement);

    @Update("UPDATE app_announcement set start_time=#{startTime},end_time=#{endTime},announcement=#{announcement},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(AppAnnouncement appAnnouncement);

    @Delete("DELETE FROM app_announcement WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update app_announcement set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,start_time,end_time,announcement,is_enabled,remark,creator,updater,create_time,update_time FROM app_announcement WHERE id=#{id} ")
    @Results(id = "appAnnouncement-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "startTime", column = "start_time"),
            @Result(property = "endTime", column = "end_time"),
            @Result(property = "announcement", column = "announcement"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    AppAnnouncement getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,start_time,end_time,announcement,is_enabled,remark,creator,updater,create_time,update_time FROM app_announcement WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "appAnnouncement-mapping")
    AppAnnouncement getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


    //update status sqls
    @Update("update app_announcement set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
