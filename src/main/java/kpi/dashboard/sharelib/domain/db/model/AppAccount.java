package kpi.dashboard.sharelib.domain.db.model;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serializable;
import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppAccount extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String userid;
    private String code;
    private String name;
    private String nameEn;
    private String gender;
    private String avatar;
    private String qrCode;
    private String mobile;
    private Date regTime;
    private String regChannel;
    private String email;
    private String bizMail;
    private String address;
    private Boolean isEnabled;

}
