package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.MainBaseAdminEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AppAccountRole extends MainBaseAdminEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String roleType;
    private String roleName;
    private String indicatorType;
    private String indicatorName;
    private String cardName;
    private String filter1;
    private String filter2;
    private String filter3;
    private String filter4;
    private String filter5;
    private String filter6;
    private String remark;
    private Date insertTime;

}