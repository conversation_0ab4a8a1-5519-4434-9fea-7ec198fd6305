package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.ApiCallLog;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface ApiCallLogMapper extends BaseMapper<ApiCallLog> {

     @Insert("INSERT IGNORE INTO api_call_log(id,description,ip,url,method,user_id,req_param,req_body,resp_code,resp_message,resp_body,exception_message,exectime)" +
                " VALUES(#{id},#{description},#{ip},#{url},#{method},#{userId},#{reqParam},#{reqBody},#{respCode},#{respMessage},#{respBody},#{exceptionMessage},#{exectime})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(ApiCallLog apiCallLog);

    @Insert("INSERT INTO api_call_log(id,description,ip,url,method,user_id,req_param,req_body,resp_code,resp_message,resp_body,exception_message,exectime)" +
                " VALUES(#{id},#{description},#{ip},#{url},#{method},#{userId},#{reqParam},#{reqBody},#{respCode},#{respMessage},#{respBody},#{exceptionMessage},#{exectime})" +
                " ON DUPLICATE KEY UPDATE description=VALUES(description),ip=VALUES(ip),url=VALUES(url),method=VALUES(method),user_id=VALUES(user_id),req_param=VALUES(req_param),req_body=VALUES(req_body),resp_code=VALUES(resp_code),resp_message=VALUES(resp_message),resp_body=VALUES(resp_body),exception_message=VALUES(exception_message),exectime=VALUES(exectime)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(ApiCallLog apiCallLog);

    @Update("UPDATE api_call_log set description=#{description},ip=#{ip},url=#{url},method=#{method},user_id=#{userId},req_param=#{reqParam},req_body=#{reqBody},resp_code=#{respCode},resp_message=#{respMessage},resp_body=#{respBody},exception_message=#{exceptionMessage},exectime=#{exectime} WHERE id=#{id}" )
    int updateByEntity(ApiCallLog apiCallLog);

    @Delete("DELETE FROM api_call_log WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update api_call_log set is_deleted=true WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,description,ip,url,method,user_id,req_param,req_body,resp_code,resp_message,resp_body,exception_message,exectime,create_time FROM api_call_log WHERE id=#{id} ")
    @Results(id = "apiCallLog-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "description", column = "description"),
      @Result(property = "ip", column = "ip"),
      @Result(property = "url", column = "url"),
      @Result(property = "method", column = "method"),
      @Result(property = "userId", column = "user_id"),
      @Result(property = "reqParam", column = "req_param"),
      @Result(property = "reqBody", column = "req_body"),
      @Result(property = "respCode", column = "resp_code"),
      @Result(property = "respMessage", column = "resp_message"),
      @Result(property = "respBody", column = "resp_body"),
      @Result(property = "exceptionMessage", column = "exception_message"),
      @Result(property = "exectime", column = "exectime"),
      @Result(property = "createTime", column = "create_time")
    })
    ApiCallLog getByIdEX(@Param("id") Long id);

    @Select("SELECT id,description,ip,url,method,user_id,req_param,req_body,resp_code,resp_message,resp_body,exception_message,exectime,create_time FROM api_call_log WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "apiCallLog-mapping")
    ApiCallLog getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


//update status sqls


//get data by foreign keys

}
