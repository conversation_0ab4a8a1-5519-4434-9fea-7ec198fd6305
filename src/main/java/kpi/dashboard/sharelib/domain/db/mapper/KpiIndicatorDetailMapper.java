package kpi.dashboard.sharelib.domain.db.mapper;



import kpi.dashboard.sharelib.domain.db.model.KpiIndicatorDetail;
import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

@Mapper
public interface KpiIndicatorDetailMapper extends BaseMapper<KpiIndicatorDetail> {

     @Insert("INSERT IGNORE INTO kpi_indicator_detail(id,`name`,code,kpi_code,sql_template_id,target_table_name,is_mtd_support,is_qtd_support,is_ytd_support,is_personal_support,is_enabled,remark,creator,updater)" +
                " VALUES(#{id},#{name},#{code},#{kpiCode},#{sqlTemplateId},#{targetTableName},#{isMtdSupport},#{isQtdSupport},#{isYtdSupport},#{isPersonalSupport},#{isEnabled},#{remark},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(KpiIndicatorDetail kpiIndicatorDetail);

    @Insert("INSERT INTO kpi_indicator_detail(id,`name`,code,kpi_code,sql_template_id,target_table_name,is_mtd_support,is_qtd_support,is_ytd_support,is_personal_support,is_enabled,remark,creator,updater)" +
                " VALUES(#{id},#{name},#{code},#{kpiCode},#{sqlTemplateId},#{targetTableName},#{isMtdSupport},#{isQtdSupport},#{isYtdSupport},#{isPersonalSupport},#{isEnabled},#{remark},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE `name`=VALUES(`name`),code=VALUES(code),kpi_code=VALUES(kpi_code),sql_template_id=VALUES(sql_template_id),target_table_name=VALUES(target_table_name),is_mtd_support=VALUES(is_mtd_support),is_qtd_support=VALUES(is_qtd_support),is_ytd_support=VALUES(is_ytd_support),is_personal_support=VALUES(is_personal_support),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(KpiIndicatorDetail kpiIndicatorDetail);

    @Update("UPDATE kpi_indicator_detail set name=#{name},code=#{code},kpi_code=#{kpiCode},sql_template_id=#{sqlTemplateId},target_table_name=#{targetTableName},is_mtd_support=#{isMtdSupport},is_qtd_support=#{isQtdSupport},is_ytd_support=#{isYtdSupport},is_personal_support=#{isPersonalSupport},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(KpiIndicatorDetail kpiIndicatorDetail);

    @Delete("DELETE FROM kpi_indicator_detail WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_indicator_detail set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,`name`,code,kpi_code,sql_template_id,target_table_name,is_mtd_support,is_qtd_support,is_ytd_support,is_personal_support,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_indicator_detail WHERE id=#{id} ")
    @Results(id = "kpiIndicatorDetail-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "name", column = "name"),
      @Result(property = "code", column = "code"),
      @Result(property = "kpiCode", column = "kpi_code"),
      @Result(property = "sqlTemplateId", column = "sql_template_id"),
      @Result(property = "targetTableName", column = "target_table_name"),
      @Result(property = "isMtdSupport", column = "is_mtd_support"),
      @Result(property = "isQtdSupport", column = "is_qtd_support"),
      @Result(property = "isYtdSupport", column = "is_ytd_support"),
      @Result(property = "isPersonalSupport", column = "is_personal_support"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    KpiIndicatorDetail getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,`name`,code,kpi_code,sql_template_id,target_table_name,is_mtd_support,is_qtd_support,is_ytd_support,is_personal_support,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_indicator_detail WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiIndicatorDetail-mapping")
    KpiIndicatorDetail getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


//update status sqls
    @Update("update kpi_indicator_detail set is_mtd_support=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsMtdSupport( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );


    @Update("update kpi_indicator_detail set is_qtd_support=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsQtdSupport( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );


    @Update("update kpi_indicator_detail set is_ytd_support=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsYtdSupport( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );


    @Update("update kpi_indicator_detail set is_personal_support=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsPersonalSupport( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );


    @Update("update kpi_indicator_detail set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled( @Param("id") Integer id , @Param("input") Boolean input,@Param("updater") String updater );

    @Select("SELECT id,`name`,code,kpi_code,sql_template_id,target_table_name,is_mtd_support,is_qtd_support,is_ytd_support,is_personal_support,is_enabled,remark,creator,updater,create_time,update_time FROM kpi_indicator_detail WHERE kpi_code=#{kpiCode} ")
    @ResultMap(value = "kpiIndicatorDetail-mapping")
    List<KpiIndicatorDetail> getByKpiCode(String kpiCode);

//get data by foreign keys

}
