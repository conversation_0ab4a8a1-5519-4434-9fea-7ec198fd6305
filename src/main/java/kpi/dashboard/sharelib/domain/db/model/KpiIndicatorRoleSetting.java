package kpi.dashboard.sharelib.domain.db.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import kpi.dashboard.sharelib.common.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

/**
 * KPI 指标角色设置实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class KpiIndicatorRoleSetting extends BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer kpiIndicatorId;
    private String roleName;
    private String settingCode;
    private String settingName;
    private Integer sort;
    private Boolean isEnabled;
    private String desc;
}