package kpi.dashboard.sharelib.domain.db.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import kpi.dashboard.sharelib.common.MainBaseAdminEntity;

import java.util.Date;
import lombok.Data;
import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ApiCallLog extends MainBaseAdminEntity implements Serializable{
    @TableId(type = IdType.AUTO)
	private Long id;
	private String description;
	private String ip;
	private String url;
	private String method;
	private String userId;
	private String reqParam;
	private String reqBody;
	private String respCode;
	private String respMessage;
	private String respBody;
	private String exceptionMessage;
	private String exectime;

}
