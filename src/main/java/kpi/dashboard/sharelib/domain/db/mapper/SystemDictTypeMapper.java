package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.SystemDictType;
import org.apache.ibatis.annotations.*;

@Mapper
public interface SystemDictTypeMapper extends BaseMapper<SystemDictType> {

    @Insert("INSERT IGNORE INTO system_dict_type(id,`name`,code,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{name},#{code},#{isEnabled},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(SystemDictType systemDictType);

    @Insert("INSERT INTO system_dict_type(id,`name`,code,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{name},#{code},#{isEnabled},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE `name`=VALUES(`name`),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(SystemDictType systemDictType);

    @Update("UPDATE system_dict_type set name=#{name},code=#{code},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(SystemDictType systemDictType);

    @Delete("DELETE FROM system_dict_type WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update system_dict_type set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,`name`,code,is_enabled,remark,creator,updater,create_time,update_time FROM system_dict_type WHERE id=#{id} ")
    @Results(id = "systemDictType-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "name", column = "name"),
            @Result(property = "code", column = "code"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    SystemDictType getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,`name`,code,is_enabled,remark,creator,updater,create_time,update_time FROM system_dict_type WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "systemDictType-mapping")
    SystemDictType getByIdFilterIsDeleted(@Param("id") Integer id);

    //get data by unique keys
    @Select("SELECT id,`name`,code,is_enabled,remark,creator,updater,create_time,update_time FROM system_dict_type WHERE code=#{code} ")
    @ResultMap(value = "systemDictType-mapping")
    SystemDictType getByCode(@Param("code") String code);

    @Select("SELECT id,`name`,code,is_enabled,remark,creator,updater,create_time,update_time FROM system_dict_type WHERE code=#{code} and is_enabled = true ")
    @ResultMap(value = "systemDictType-mapping")
    SystemDictType getValidByCode(@Param("code") String code);


    //update status sqls
    @Update("update system_dict_type set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
