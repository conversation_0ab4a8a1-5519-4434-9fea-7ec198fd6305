package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.SysRoleAccount;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SysRoleAccountMapper extends Base<PERSON><PERSON>per<SysRoleAccount> {

     @Insert("INSERT IGNORE INTO sys_role_account(id,role_id,account_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{roleId},#{accountId},#{createdByUser},#{updatedByUser},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(SysRoleAccount sysRoleAccount);

    @Insert("INSERT INTO sys_role_account(id,role_id,account_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{roleId},#{accountId},#{createdByUser},#{updatedByUser},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE updated_by_user=VALUES(updated_by_user),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(SysRoleAccount sysRoleAccount);

    @Update("UPDATE sys_role_account set role_id=#{roleId},account_id=#{accountId},updated_by_user=#{updatedByUser},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(SysRoleAccount sysRoleAccount);

    @Delete("DELETE FROM sys_role_account WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update sys_role_account set is_deleted=true, updated_by_user=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,role_id,account_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_account WHERE id=#{id} ")
    @Results(id = "sysRoleAccount-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "roleId", column = "role_id"),
      @Result(property = "accountId", column = "account_id"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    SysRoleAccount getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,role_id,account_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_account WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "sysRoleAccount-mapping")
    SysRoleAccount getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,role_id,account_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_role_account WHERE role_id=#{roleId} and account_id=#{accountId} ")
    @ResultMap(value = "sysRoleAccount-mapping")
    SysRoleAccount getByRoleIdAccountId(@Param("roleId") Integer roleId,@Param("accountId") Integer accountId);



//update status sqls


//get data by foreign keys

}
