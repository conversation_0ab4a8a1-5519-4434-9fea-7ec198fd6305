package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiDataSyncTask;
import org.apache.ibatis.annotations.*;

@Mapper
public interface KpiDataSyncTaskMapper extends BaseMapper<KpiDataSyncTask> {

    @Insert("INSERT IGNORE INTO kpi_data_sync_task(id,task_name,source_table_name,target_table_name,is_suc,data_time,job_start_time,job_end_time,has_finished,suc_count,failed_count,creator,updater)" +
            " VALUES(#{id},#{taskName},#{sourceTableName},#{targetTableName},#{isSuc},#{dataTime},#{jobStartTime},#{jobEndTime},#{hasFinished},#{sucCount},#{failedCount},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiDataSyncTask kpiDataSyncTask);

    @Insert("INSERT INTO kpi_data_sync_task(id,task_name,source_table_name,target_table_name,is_suc,data_time,job_start_time,job_end_time,has_finished,suc_count,failed_count,creator,updater)" +
            " VALUES(#{id},#{taskName},#{sourceTableName},#{targetTableName},#{isSuc},#{dataTime},#{jobStartTime},#{jobEndTime},#{hasFinished},#{sucCount},#{failedCount},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE task_name=VALUES(task_name),source_table_name=VALUES(source_table_name),target_table_name=VALUES(target_table_name),is_suc=VALUES(is_suc),data_time=VALUES(data_time),job_start_time=VALUES(job_start_time),job_end_time=VALUES(job_end_time),has_finished=VALUES(has_finished),suc_count=VALUES(suc_count),failed_count=VALUES(failed_count),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiDataSyncTask kpiDataSyncTask);

    @Update("UPDATE kpi_data_sync_task set task_name=#{taskName},source_table_name=#{sourceTableName},target_table_name=#{targetTableName},is_suc=#{isSuc},data_time=#{dataTime},job_start_time=#{jobStartTime},job_end_time=#{jobEndTime},has_finished=#{hasFinished},suc_count=#{sucCount},failed_count=#{failedCount},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiDataSyncTask kpiDataSyncTask);

    @Delete("DELETE FROM kpi_data_sync_task WHERE id=#{id}")
    int deleteByIdEX(@Param("id") String id);

    @Delete("Update kpi_data_sync_task set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") String id);

    @Select("SELECT id,task_name,source_table_name,target_table_name,is_suc,data_time,job_start_time,job_end_time,has_finished,suc_count,failed_count,creator,updater,create_time,update_time FROM kpi_data_sync_task WHERE id=#{id} ")
    @Results(id = "kpiDataSyncTask-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "taskName", column = "task_name"),
            @Result(property = "sourceTableName", column = "source_table_name"),
            @Result(property = "targetTableName", column = "target_table_name"),
            @Result(property = "isSuc", column = "is_suc"),
            @Result(property = "dataTime", column = "data_time"),
            @Result(property = "jobStartTime", column = "job_start_time"),
            @Result(property = "jobEndTime", column = "job_end_time"),
            @Result(property = "hasFinished", column = "has_finished"),
            @Result(property = "sucCount", column = "suc_count"),
            @Result(property = "failedCount", column = "failed_count"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiDataSyncTask getByIdEX(@Param("id") String id);

    @Select("SELECT id,task_name,source_table_name,target_table_name,is_suc,data_time,job_start_time,job_end_time,has_finished,suc_count,failed_count,creator,updater,create_time,update_time FROM kpi_data_sync_task WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiDataSyncTask-mapping")
    KpiDataSyncTask getByIdFilterIsDeleted(@Param("id") String id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_data_sync_task set is_suc=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsSuc(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
