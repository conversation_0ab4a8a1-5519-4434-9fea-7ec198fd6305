package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.SysMenu;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {

     @Insert("INSERT IGNORE INTO sys_menu(id,title,menu_type,menu_code,menu_text,parent_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{title},#{menuType},#{menuCode},#{menuText},#{parentId},#{createdByUser},#{updatedByUser},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(SysMenu sysMenu);

    @Insert("INSERT INTO sys_menu(id,title,menu_type,menu_code,menu_text,parent_id,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{title},#{menuType},#{menuCode},#{menuText},#{parentId},#{createdByUser},#{updatedByUser},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE title=VALUES(title),menu_type=VALUES(menu_type),menu_text=VALUES(menu_text),parent_id=VALUES(parent_id),updated_by_user=VALUES(updated_by_user),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(SysMenu sysMenu);

    @Update("UPDATE sys_menu set title=#{title},menu_type=#{menuType},menu_code=#{menuCode},menu_text=#{menuText},parent_id=#{parentId},updated_by_user=#{updatedByUser},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(SysMenu sysMenu);

    @Delete("DELETE FROM sys_menu WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update sys_menu set is_deleted=true, updated_by_user=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,title,menu_type,menu_code,menu_text,parent_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_menu WHERE id=#{id} ")
    @Results(id = "sysMenu-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "title", column = "title"),
      @Result(property = "menuType", column = "menu_type"),
      @Result(property = "menuCode", column = "menu_code"),
      @Result(property = "menuText", column = "menu_text"),
      @Result(property = "parentId", column = "parent_id"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    SysMenu getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,title,menu_type,menu_code,menu_text,parent_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_menu WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "sysMenu-mapping")
    SysMenu getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,title,menu_type,menu_code,menu_text,parent_id,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_menu WHERE menu_code=#{menuCode} ")
    @ResultMap(value = "sysMenu-mapping")
    SysMenu getByMenuCode(@Param("menuCode") String menuCode);



//update status sqls


//get data by foreign keys

}
