package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.AppRole;
import org.apache.ibatis.annotations.*;

@Mapper
public interface AppRoleMapper extends BaseMapper<AppRole> {

    @Insert("INSERT IGNORE INTO app_role(id,`name`,code,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{name},#{code},#{sort},#{isEnabled},#{remark},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(AppRole appRole);

    @Insert("INSERT INTO app_role(id,`name`,code,sort,is_enabled,remark,creator,updater)" +
            " VALUES(#{id},#{name},#{code},#{sort},#{isEnabled},#{remark},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE `name`=VALUES(`name`),sort=VALUES(sort),is_enabled=VALUES(is_enabled),remark=VALUES(remark),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(AppRole appRole);

    @Update("UPDATE app_role set name=#{name},code=#{code},sort=#{sort},is_enabled=#{isEnabled},remark=#{remark},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(AppRole appRole);

    @Delete("DELETE FROM app_role WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update app_role set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,`name`,code,sort,is_enabled,remark,creator,updater,create_time,update_time FROM app_role WHERE id=#{id} ")
    @Results(id = "appRole-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "name", column = "name"),
            @Result(property = "code", column = "code"),
            @Result(property = "sort", column = "sort"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    AppRole getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,`name`,code,sort,is_enabled,remark,creator,updater,create_time,update_time FROM app_role WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "appRole-mapping")
    AppRole getByIdFilterIsDeleted(@Param("id") Integer id);

    //get data by unique keys
    @Select("SELECT id,`name`,code,sort,is_enabled,remark,creator,updater,create_time,update_time FROM app_role WHERE code=#{code} ")
    @ResultMap(value = "appRole-mapping")
    AppRole getByCode(@Param("code") String code);

    @Select("SELECT id,`name`,code,sort,is_enabled,remark,creator,updater,create_time,update_time FROM app_role WHERE code=#{code} and is_enabled = true ")
    @ResultMap(value = "appRole-mapping")
    AppRole getValidByCode(@Param("code") String code);


    //update status sqls
    @Update("update app_role set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
