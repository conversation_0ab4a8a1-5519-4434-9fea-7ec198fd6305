package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiCategory;
import org.apache.ibatis.annotations.*;

@Mapper
public interface KpiCategoryMapper extends BaseMapper<KpiCategory> {

    @Insert("INSERT IGNORE INTO kpi_category(id,category_name,icon,is_enabled,creator,updater)" +
            " VALUES(#{id},#{categoryName},#{icon},#{isEnabled},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiCategory kpiCategory);

    @Insert("INSERT INTO kpi_category(id,category_name,icon,is_enabled,creator,updater)" +
            " VALUES(#{id},#{categoryName},#{icon},#{isEnabled},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE category_name=VALUES(category_name),icon=VALUES(icon),is_enabled=VALUES(is_enabled),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiCategory kpiCategory);

    @Update("UPDATE kpi_category set category_name=#{categoryName},icon=#{icon},is_enabled=#{isEnabled},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiCategory kpiCategory);

    @Delete("DELETE FROM kpi_category WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update kpi_category set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,category_name,icon,is_enabled,creator,updater,create_time,update_time FROM kpi_category WHERE id=#{id} ")
    @Results(id = "kpiCategory-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "categoryName", column = "category_name"),
            @Result(property = "icon", column = "icon"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiCategory getByIdEX(@Param("id") Long id);

    @Select("SELECT id,category_name,icon,is_enabled,creator,updater,create_time,update_time FROM kpi_category WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiCategory-mapping")
    KpiCategory getByIdFilterIsDeleted(@Param("id") Long id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_category set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
