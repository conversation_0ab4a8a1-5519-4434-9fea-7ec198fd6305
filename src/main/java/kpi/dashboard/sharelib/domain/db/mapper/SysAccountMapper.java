package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.SysAccount;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SysAccountMapper extends BaseMapper<SysAccount> {

     @Insert("INSERT IGNORE INTO sys_account(id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{platform},#{staffCode},#{userName},#{nickName},#{password},#{cellphone},#{unionid},#{isEnabled},#{isDeleted},#{remark},#{createdByUser},#{updatedByUser},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(SysAccount sysAccount);

    @Insert("INSERT INTO sys_account(id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater)" +
                " VALUES(#{id},#{platform},#{staffCode},#{userName},#{nickName},#{password},#{cellphone},#{unionid},#{isEnabled},#{isDeleted},#{remark},#{createdByUser},#{updatedByUser},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE platform=VALUES(platform),staff_code=VALUES(staff_code),nick_name=VALUES(nick_name),password=VALUES(password),cellphone=VALUES(cellphone),unionid=VALUES(unionid),is_enabled=VALUES(is_enabled),is_deleted=VALUES(is_deleted),remark=VALUES(remark),updated_by_user=VALUES(updated_by_user),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(SysAccount sysAccount);

    @Update("UPDATE sys_account set platform=#{platform},staff_code=#{staffCode},user_name=#{userName},nick_name=#{nickName},password=#{password},cellphone=#{cellphone},unionid=#{unionid},is_enabled=#{isEnabled},is_deleted=#{isDeleted},remark=#{remark},updated_by_user=#{updatedByUser},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(SysAccount sysAccount);

    @Delete("DELETE FROM sys_account WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update sys_account set is_deleted=true, updated_by_user=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_account WHERE id=#{id} ")
    @Results(id = "sysAccount-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "platform", column = "platform"),
      @Result(property = "staffCode", column = "staff_code"),
      @Result(property = "userName", column = "user_name"),
      @Result(property = "nickName", column = "nick_name"),
      @Result(property = "password", column = "password"),
      @Result(property = "cellphone", column = "cellphone"),
      @Result(property = "unionid", column = "unionid"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "isDeleted", column = "is_deleted"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    SysAccount getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_account WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "sysAccount-mapping")
    SysAccount getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_account WHERE user_name=#{userName} ")
    @ResultMap(value = "sysAccount-mapping")
    SysAccount getByUserName(@Param("userName") String userName);

    @Select("SELECT id,platform,staff_code,user_name,nick_name,password,cellphone,unionid,is_enabled,is_deleted,remark,created_by_user,updated_by_user,creator,updater,create_time,update_time FROM sys_account WHERE user_name=#{userName} and is_enabled = true and is_deleted = false ")
    @ResultMap(value = "sysAccount-mapping")
    SysAccount getValidByUserName(@Param("userName") String userName);



//update status sqls


//get data by foreign keys

}
