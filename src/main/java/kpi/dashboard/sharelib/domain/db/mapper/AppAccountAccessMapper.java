package kpi.dashboard.sharelib.domain.db.mapper;


import java.util.Date;


import kpi.dashboard.sharelib.domain.db.model.AppAccountAccess;
import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

@Mapper
public interface AppAccountAccessMapper extends BaseMapper<AppAccountAccess> {

     @Insert("INSERT IGNORE INTO app_account_access(id,stat_month,user_code,user_name,user_name_en,user_email,role_type,role_name,access_type,level_1,level_2,level_3,level_4,level_5,level_6,source_,remark,insert_time,creator,updater)" +
                " VALUES(#{id},#{statMonth},#{userCode},#{userName},#{userNameEn},#{userEmail},#{roleType},#{roleName},#{accessType},#{level1},#{level2},#{level3},#{level4},#{level5},#{level6},#{source},#{remark},#{insertTime},#{creator},#{updater})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(AppAccountAccess appAccountAccess);

    @Insert("INSERT INTO app_account_access(id,stat_month,user_code,user_name,user_name_en,user_email,role_type,role_name,access_type,level_1,level_2,level_3,level_4,level_5,level_6,source_,remark,insert_time,creator,updater)" +
                " VALUES(#{id},#{statMonth},#{userCode},#{userName},#{userNameEn},#{userEmail},#{roleType},#{role_name},#{accessType},#{level1},#{level2},#{level3},#{level4},#{level5},#{level6},#{source},#{remark},#{insertTime},#{creator},#{updater})" +
                " ON DUPLICATE KEY UPDATE stat_month=VALUES(stat_month),user_code=VALUES(user_code),user_name=VALUES(user_name),user_name_en=VALUES(user_name_en),user_email=VALUES(user_email),role_type=VALUES(role_type),role_name=VALUES(role_name),access_type=VALUES(access_type),level_1=VALUES(level_1),level_2=VALUES(level_2),level_3=VALUES(level_3),level_4=VALUES(level_4),level_5=VALUES(level_5),level_6=VALUES(level_6),source_=VALUES(source_),remark=VALUES(remark),insert_time=VALUES(insert_time),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(AppAccountAccess appAccountAccess);

    @Update("UPDATE app_account_access set stat_month=#{statMonth},user_code=#{userCode},user_name=#{userName},user_name_en=#{userNameEn},user_email=#{userEmail},role_type=#{roleType},role_name=#{roleName},access_type=#{accessType},level_1=#{level1},level_2=#{level2},level_3=#{level3},level_4=#{level4},level_5=#{level5},level_6=#{level6},source_=#{source},remark=#{remark},insert_time=#{insertTime},updater=#{updater} WHERE id=#{id}" )
    int updateByEntity(AppAccountAccess appAccountAccess);

    @Delete("DELETE FROM app_account_access WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Long id);

    @Delete("Update app_account_access set is_deleted=true, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Long id);

    @Select("SELECT id,stat_month,user_code,user_name,user_name_en,user_email,role_type,role_name,access_type,level_1,level_2,level_3,level_4,level_5,level_6,source_,remark,insert_time,creator,updater,create_time,update_time FROM app_account_access WHERE id=#{id} ")
    @Results(id = "appAccountAccess-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "statMonth", column = "stat_month"),
      @Result(property = "userCode", column = "user_code"),
      @Result(property = "userName", column = "user_name"),
      @Result(property = "userNameEn", column = "user_name_en"),
      @Result(property = "userEmail", column = "user_email"),
      @Result(property = "roleType", column = "role_type"),
      @Result(property = "roleName", column = "role_name"),
      @Result(property = "accessType", column = "access_type"),
      @Result(property = "level1", column = "level_1"),
      @Result(property = "level2", column = "level_2"),
      @Result(property = "level3", column = "level_3"),
      @Result(property = "level4", column = "level_4"),
      @Result(property = "level5", column = "level_5"),
      @Result(property = "level6", column = "level_6"),
      @Result(property = "source", column = "source_"),
      @Result(property = "remark", column = "remark"),
      @Result(property = "insertTime", column = "insert_time"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time")
    })
    AppAccountAccess getByIdEX(@Param("id") Long id);

    @Select("SELECT id,stat_month,user_code,user_name,user_name_en,user_email,role_type,role_name,access_type,level_1,level_2,level_3,level_4,level_5,level_6,source_,remark,insert_time,creator,updater,create_time,update_time FROM app_account_access WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "appAccountAccess-mapping")
    AppAccountAccess getByIdFilterIsDeleted(@Param("id") Long id);

    @Select("SELECT id,stat_month,user_code,user_name,user_name_en,user_email,role_type,role_name,access_type,level_1,level_2,level_3,level_4,level_5,level_6,source_,remark,insert_time,creator,updater,create_time,update_time FROM app_account_access WHERE stat_month = (SELECT MAX(stat_month) FROM app_account_access WHERE user_email = #{userEmail}) AND user_email = #{userEmail}")
    List<AppAccountAccess> getUserDataByMaxStatMonth(@Param("userEmail") String userEmail);

    @Select("select * from app_account_access where user_email=#{userEmail} and stat_month in(\n" +
            "\n" +
            "select max(stat_month) from app_account_access where user_email=#{userEmail}) order by level_1,level_2,level_3 asc")
    @ResultMap(value = "appAccountAccess-mapping")
    List<AppAccountAccess> selectByEmail(@Param("userEmail") String userEmail);

    @Select("select * from app_account_access where user_email=#{userEmail} and stat_month in(\n" +
            "\n" +
            "select max(stat_month) from app_account_access where user_email=#{userEmail}) LIMIT 1")
    @ResultMap(value = "appAccountAccess-mapping")
    AppAccountAccess selectByEmailLimit(@Param("userEmail") String userEmail);


//get data by unique keys


//update status sqls


//get data by foreign keys

}
