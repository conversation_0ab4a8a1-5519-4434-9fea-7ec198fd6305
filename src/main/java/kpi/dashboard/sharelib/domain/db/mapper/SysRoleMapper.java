package kpi.dashboard.sharelib.domain.db.mapper;


import kpi.dashboard.sharelib.domain.db.model.SysRole;

import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

     @Insert("INSERT IGNORE INTO sys_role(id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,role_code)" +
                " VALUES(#{id},#{roleName},#{roleType},#{isEnabled},#{canDelete},#{isDeleted},#{createdByUser},#{updatedByUser},#{creator},#{updater},#{roleCode})")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertIgnoreEntity(SysRole sysRole);

    @Insert("INSERT INTO sys_role(id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,role_code)" +
                " VALUES(#{id},#{roleName},#{roleType},#{isEnabled},#{canDelete},#{isDeleted},#{createdByUser},#{updatedByUser},#{creator},#{updater},#{roleCode})" +
                " ON DUPLICATE KEY UPDATE role_name=VALUES(role_name),role_type=VALUES(role_type),is_enabled=VALUES(is_enabled),can_delete=VALUES(can_delete),is_deleted=VALUES(is_deleted),updated_by_user=VALUES(updated_by_user),updater=VALUES(updater)")
     @Options(useGeneratedKeys = true, keyProperty = "id")
     int insertUpdateEntity(SysRole sysRole);

    @Update("UPDATE sys_role set role_name=#{roleName},role_type=#{roleType},is_enabled=#{isEnabled},can_delete=#{canDelete},is_deleted=#{isDeleted},updated_by_user=#{updatedByUser},updater=#{updater},role_code=#{roleCode} WHERE id=#{id}" )
    int updateByEntity(SysRole sysRole);

    @Delete("DELETE FROM sys_role WHERE id=#{id}" )
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update sys_role set is_deleted=true, updated_by_user=#{userKey}, updater=#{userKey} WHERE id=#{id}" )
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,create_time,update_time,role_code FROM sys_role WHERE id=#{id} ")
    @Results(id = "sysRole-mapping", value = {
      @Result(property = "id", column = "id"),
      @Result(property = "roleName", column = "role_name"),
      @Result(property = "roleType", column = "role_type"),
      @Result(property = "isEnabled", column = "is_enabled"),
      @Result(property = "canDelete", column = "can_delete"),
      @Result(property = "isDeleted", column = "is_deleted"),
      @Result(property = "createdByUser", column = "created_by_user"),
      @Result(property = "updatedByUser", column = "updated_by_user"),
      @Result(property = "creator", column = "creator"),
      @Result(property = "updater", column = "updater"),
      @Result(property = "createTime", column = "create_time"),
      @Result(property = "updateTime", column = "update_time"),
      @Result(property = "roleCode", column = "role_code")
    })
    SysRole getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,create_time,update_time,role_code FROM sys_role WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "sysRole-mapping")
    SysRole getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys
    @Select("SELECT id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,create_time,update_time,role_code FROM sys_role WHERE role_code=#{roleCode} ")
    @ResultMap(value = "sysRole-mapping")
    SysRole getByRoleCode(@Param("roleCode") String roleCode);

    @Select("SELECT id,role_name,role_type,is_enabled,can_delete,is_deleted,created_by_user,updated_by_user,creator,updater,create_time,update_time,role_code FROM sys_role WHERE role_code=#{roleCode} and is_enabled = true and is_deleted = false ")
    @ResultMap(value = "sysRole-mapping")
    SysRole getValidByRoleCode(@Param("roleCode") String roleCode);



//update status sqls


//get data by foreign keys

}
