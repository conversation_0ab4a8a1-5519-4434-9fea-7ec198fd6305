package kpi.dashboard.sharelib.domain.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import kpi.dashboard.sharelib.domain.db.model.KpiGroup;
import org.apache.ibatis.annotations.*;

@Mapper
public interface KpiGroupMapper extends BaseMapper<KpiGroup> {

    @Insert("INSERT IGNORE INTO kpi_group(id,group_name,icon,is_enabled,creator,updater)" +
            " VALUES(#{id},#{groupName},#{icon},#{isEnabled},#{creator},#{updater})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertIgnoreEntity(KpiGroup kpiGroup);

    @Insert("INSERT INTO kpi_group(id,group_name,icon,is_enabled,creator,updater)" +
            " VALUES(#{id},#{groupName},#{icon},#{isEnabled},#{creator},#{updater})" +
            " ON DUPLICATE KEY UPDATE group_name=VALUES(group_name),icon=VALUES(icon),is_enabled=VALUES(is_enabled),updater=VALUES(updater)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertUpdateEntity(KpiGroup kpiGroup);

    @Update("UPDATE kpi_group set group_name=#{groupName},icon=#{icon},is_enabled=#{isEnabled},updater=#{updater} WHERE id=#{id}")
    int updateByEntity(KpiGroup kpiGroup);

    @Delete("DELETE FROM kpi_group WHERE id=#{id}")
    int deleteByIdEX(@Param("id") Integer id);

    @Delete("Update kpi_group set is_deleted=true, updater=#{userKey} WHERE id=#{id}")
    int deleteByIdLogically(@Param("userKey") Object userKey, @Param("id") Integer id);

    @Select("SELECT id,group_name,icon,is_enabled,creator,updater,create_time,update_time FROM kpi_group WHERE id=#{id} ")
    @Results(id = "kpiGroup-mapping", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "groupName", column = "group_name"),
            @Result(property = "icon", column = "icon"),
            @Result(property = "isEnabled", column = "is_enabled"),
            @Result(property = "creator", column = "creator"),
            @Result(property = "updater", column = "updater"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time")
    })
    KpiGroup getByIdEX(@Param("id") Integer id);

    @Select("SELECT id,group_name,icon,is_enabled,creator,updater,create_time,update_time FROM kpi_group WHERE id=#{id} and is_deleted=false ")
    @ResultMap(value = "kpiGroup-mapping")
    KpiGroup getByIdFilterIsDeleted(@Param("id") Integer id);

//get data by unique keys


    //update status sqls
    @Update("update kpi_group set is_enabled=#{input} , updater=#{updater} WHERE id=#{id}")
    int updateIsEnabled(@Param("id") Integer id, @Param("input") Boolean input, @Param("updater") String updater);


//get data by foreign keys

}
