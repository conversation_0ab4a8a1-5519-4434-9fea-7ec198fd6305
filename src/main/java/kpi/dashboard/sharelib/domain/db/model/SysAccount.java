package kpi.dashboard.sharelib.domain.db.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import kpi.dashboard.sharelib.common.MainBaseAdminEntity;

import java.util.Date;
import lombok.Data;
import java.io.Serializable;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SysAccount extends MainBaseAdminEntity implements Serializable{
    @TableId(type = IdType.AUTO)
	private Integer id;
	private String platform;
	private String staffCode;
	private String userName;
	private String nickName;
	private String password;
	private String cellphone;
	private String unionid;
	private Integer isEnabled;
	private Integer isDeleted;
	private String remark;

}
