package kpi.dashboard.sharelib.domain.db.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode
public class DimMobileBiRoleIndicatorMapping {
    private Integer id;
    private String statMonth;
    private String endMonth;
    private String roleMame;
    private String indicatorName;
    private String indicatorValue;
    private String indicatorDimName;
    private String dimTableName;
    private Date insertTime;
    private Date updateTime;
    private String updater;

}
