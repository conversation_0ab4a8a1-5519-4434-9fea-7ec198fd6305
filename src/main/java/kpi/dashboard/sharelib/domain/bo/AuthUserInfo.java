package  kpi.dashboard.sharelib.domain.bo;

import lombok.*;

import java.util.LinkedList;
import java.util.List;

@ToString
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthUserInfo {
    private String identifier;
    private String pageRole;
    private String indicatorRole;
    private String roleType;
    private String roleName;

    private List<AuthDataDetail> authDatas = new LinkedList<>();

    @AllArgsConstructor
    @Data
    public static class AuthDataDetail {
        private String authType;
        private String authedData;
    }
}
