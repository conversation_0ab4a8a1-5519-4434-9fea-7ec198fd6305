package kpi.dashboard.sharelib.domain.dto;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;

@Slf4j
public class InternalMessage {
    private static HashMap<String, InternalMessage> messageHashMap = new HashMap<>();

    static {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        try {
            InputStream inputStream = (classLoader.getResource("internal-message.csv").openStream());
            BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, "utf-8"));
            String line = in.readLine(); //skip first line
            while ((line = in.readLine()) != null) {
                String[] content = line.split(",");
                if (content.length < 3)
                    continue;
                String key = content[0] + "," + content[1];
                if (!messageHashMap.containsKey(key)) {
                    String message = "";
                    for (int i = 2; i < content.length; i++) {
                        if (i == content.length - 1)
                            message += content[i];
                        else
                            message += content[i] + ",";
                    }
                    messageHashMap.put(key, new InternalMessage(content[0], content[1], message));
                }
            }
        } catch (IOException e) {
            log.error("Error loading internal-message.csv file");
            System.exit(-1);
        }
    }

    private String source;
    private String code;
    private String msg;

    protected InternalMessage(String source, String code, String msg) {
        this.source = source;
        this.code = code;
        this.msg = msg;
    }

    public static String getMessage(String source, String code, Object... msgObjects) {
        String key = source == null ? "," + code : source + "," + code;
        if (messageHashMap.containsKey(key)) {
            if (msgObjects == null || msgObjects.length == 0)
                return messageHashMap.get(key).msg;
            String fmt = messageHashMap.get(key).msg;
            return String.format(fmt, msgObjects);
        }
        return null;
    }

    static String getSender(String source, String code) {
        String key = source == null ? "," + code : source + "," + code;
        if (messageHashMap.containsKey(key))
            return messageHashMap.get(key).source;
        return null;
    }

    static String getIdenifier(String source, String code) {
        String key = source == null ? "," + code : source + "," + code;
        if (messageHashMap.containsKey(key))
            return messageHashMap.get(key).source;
        return null;
    }
}
