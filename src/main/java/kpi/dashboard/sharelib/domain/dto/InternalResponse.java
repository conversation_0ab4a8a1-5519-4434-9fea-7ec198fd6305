package kpi.dashboard.sharelib.domain.dto;


import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

@Slf4j
public class InternalResponse<T> {
    private final static String SUCCESS_CODE = "01";
    private final static String FAILLED_CODE = "02";
    private static String source;

    static {
        try {
            Properties props = new Properties();
            InputStream inputStream = (Thread.currentThread().getContextClassLoader().getResource("bootstrap.yml").openStream());
            props.load(inputStream);
            source = props.getProperty("name");
        } catch (IOException e) {
            log.error("Error loading app.properties file");
            System.exit(-1);
        }
    }

    private final Double version = 1.0;
    private String sender;
    private String code;
    private String message;
    private T body;

    private InternalResponse() {
    }

    private InternalResponse(String code, String source) {
        this.code = code;
        this.message = InternalMessage.getMessage(source, code);
        this.sender = InternalMessage.getSender(source, code);
    }

    private InternalResponse(String code, String source, Object... msgObjects) {
        this.code = code;
        this.message = InternalMessage.getMessage(source, code, msgObjects);
        this.sender = InternalMessage.getSender(source, code);
    }


    public static <T> InternalResponse<T> success() {
        return new InternalResponse<T>(SUCCESS_CODE, source);
    }

    public static <T> InternalResponse<T> success(String code) {
        return new InternalResponse<T>(code, source);
    }

    public static <T> InternalResponse<T> withParams(String sender, String code, String message) {
        InternalResponse<T> internalResponse = new InternalResponse<T>();
        internalResponse.sender = sender;
        internalResponse.code = code;
        internalResponse.message = message;
        return internalResponse;
    }

    public static <T> InternalResponse<T> withParamsMessage(String sender, String code) {
        InternalResponse<T> internalResponse = new InternalResponse<T>();
        internalResponse.sender = sender;
        internalResponse.code = code;
        internalResponse.message = InternalMessage.getMessage(sender, code);
        return internalResponse;
    }


    public static <T> InternalResponse<T> fail() {
        return new InternalResponse<T>(FAILLED_CODE, source);
    }

    public static <T> InternalResponse<T> fail(String code) {
        return new InternalResponse<T>(code, source);
    }

    public static <T> InternalResponse<T> fail(String code, Object... msgObjects) {
        return new InternalResponse<T>(code, source, msgObjects);
    }

    public InternalResponse<T> withBody(T body) {
        this.body = body;
        return this;
    }


    public String getMessage() {
        return message;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    @Override
    public String toString(){
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("sender=").append(sender).append(",");
        stringBuffer.append("code=").append(code).append(",");
        stringBuffer.append("message=").append(message).append(",");
        stringBuffer.append("body={");
        if(null != body){
          stringBuffer.append(body);
        }else {
          stringBuffer.append("null");
        }
        stringBuffer.append("}");
        return stringBuffer.toString();
    }
}
