#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 536870912 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3828), pid=16356, tid=39844
#
# JRE version:  (21.0.4+13) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Sat Nov 30 14:16:02 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.017933 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001dc35b13fb0):  JavaThread "Unknown thread" [_thread_in_vm, id=39844, stack(0x0000006510e00000,0x0000006510f00000) (1024K)]

Stack: [0x0000006510e00000,0x0000006510f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x6dfe65]
V  [jvm.dll+0x6d433a]
V  [jvm.dll+0x36209b]
V  [jvm.dll+0x369c46]
V  [jvm.dll+0x3bbd76]
V  [jvm.dll+0x3bc048]
V  [jvm.dll+0x33485c]
V  [jvm.dll+0x33554b]
V  [jvm.dll+0x888039]
V  [jvm.dll+0x3c8ea8]
V  [jvm.dll+0x8711d8]
V  [jvm.dll+0x45d85e]
V  [jvm.dll+0x45f541]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffb73c02108, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001dc53a10980 WorkerThread "GC Thread#0"                     [id=33932, stack(0x0000006510f00000,0x0000006511000000) (1024K)]
  0x000001dc35b8b0b0 ConcurrentGCThread "G1 Main Marker"            [id=10608, stack(0x0000006511000000,0x0000006511100000) (1024K)]
  0x000001dc35b8c350 WorkerThread "G1 Conc#0"                       [id=29208, stack(0x0000006511100000,0x0000006511200000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffb732f7917]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb73c76a48] Heap_lock - owner thread: 0x000001dc35b13fb0

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001dc4aa40000,0x000001dc4ba30000] _byte_map_base: 0x000001dc47a22000

Marking Bits: (CMBitMap*) 0x000001dc35b7a810
 Bits: [0x000001dc4ba30000, 0x000001dc53940000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.012 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6fdce0000 - 0x00007ff6fdcea000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffc144d0000 - 0x00007ffc146e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc13b50000 - 0x00007ffc13c14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc11850000 - 0x00007ffc11c09000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffc11ec0000 - 0x00007ffc11fd1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbca0d0000 - 0x00007ffbca0e8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffbe2370000 - 0x00007ffbe238b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffc13980000 - 0x00007ffc13b2e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc11c10000 - 0x00007ffc11c36000 	C:\WINDOWS\System32\win32u.dll
0x00007ffc13da0000 - 0x00007ffc13dc9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc11ce0000 - 0x00007ffc11dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffbe48f0000 - 0x00007ffbe4b82000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96\COMCTL32.dll
0x00007ffc12150000 - 0x00007ffc121ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffc12970000 - 0x00007ffc12a17000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc12800000 - 0x00007ffc12831000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000067d70000 - 0x0000000067d7d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffc138c0000 - 0x00007ffc13972000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc12590000 - 0x00007ffc12637000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc11cb0000 - 0x00007ffc11cd8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffc13c80000 - 0x00007ffc13d94000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbfd3e0000 - 0x00007ffbfd4e3000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffc12a80000 - 0x00007ffc132f6000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffc12a20000 - 0x00007ffc12a7e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffc11160000 - 0x00007ffc1116a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbe2390000 - 0x00007ffbe239c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffbb2e70000 - 0x00007ffbb2efd000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffb72fb0000 - 0x00007ffb73d67000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffc12300000 - 0x00007ffc12371000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffc116a0000 - 0x00007ffc116ed000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffc080f0000 - 0x00007ffc08124000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffc11680000 - 0x00007ffc11693000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffc10780000 - 0x00007ffc10798000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffbdd0d0000 - 0x00007ffbdd0da000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffc0f210000 - 0x00007ffc0f442000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc13fa0000 - 0x00007ffc1432f000 	C:\WINDOWS\System32\combase.dll
0x00007ffc13360000 - 0x00007ffc13437000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbfa7a0000 - 0x00007ffbfa7d2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc117d0000 - 0x00007ffc1184b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbca0b0000 - 0x00007ffbca0cf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 13932K (0% of 33293192K total physical memory with 7278864K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 8 days 4:02 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (7108M free)
TotalPageFile size 42752M (AvailPageFile size 176M)
current process WorkingSet (physical memory assigned to process): 13M, peak: 13M
current process commit charge ("private bytes"): 65M, peak: 577M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
