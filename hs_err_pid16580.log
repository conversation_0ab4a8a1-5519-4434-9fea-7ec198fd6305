#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=16580, tid=42364
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
Time: Fri Nov 29 15:16:01 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.4391) elapsed time: 0.098368 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000159e4828a50):  JavaThread "main"             [_thread_in_vm, id=42364, stack(0x000000072fb00000,0x000000072fc00000) (1024K)]

Stack: [0x000000072fb00000,0x000000072fc00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x8bba1e]
V  [jvm.dll+0x684b95]
V  [jvm.dll+0x684bfa]
V  [jvm.dll+0x687416]
V  [jvm.dll+0x6872e2]
V  [jvm.dll+0x68554e]
V  [jvm.dll+0x696569]
V  [jvm.dll+0x68f9ee]
V  [jvm.dll+0x3e28d1]
C  0x00000159eff8bbd2

The last pc belongs to method entry point (kind = zerolocals) (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.util.TreeMap.getEntry(Ljava/lang/Object;)Ljava/util/TreeMap$Entry;+0 java.base@21.0.4
j  java.util.TreeMap.get(Ljava/lang/Object;)Ljava/lang/Object;+2 java.base@21.0.4
j  java.lang.ProcessEnvironment.getenv(Ljava/lang/String;)Ljava/lang/String;+4 java.base@21.0.4
j  java.lang.System.getenv(Ljava/lang/String;)Ljava/lang/String;+40 java.base@21.0.4
j  externalApp.ExternalAppUtil.getEnv(Ljava/lang/String;)Ljava/lang/String;+9
j  git4idea.http.GitAskPassApp.main([Ljava/lang/String;)V+18
v  ~StubRoutines::call_stub 0x00000159eff8100d
method entry point (kind = zerolocals)  [0x00000159eff8ba00, 0x00000159eff8bdc0]  960 bytes
[MachCode]
  0x00000159eff8ba00: 488b 5308 | 0fb7 4a2e | 0fb7 522c | 2bd1 81fa | f501 0000 | 0f86 2500 | 0000 488b | c248 c1e0 
  0x00000159eff8ba20: 0348 83c0 | 5849 0387 | d004 0000 | 483b e00f | 870a 0000 | 0058 498b | e550 e941 | acff ff58 
  0x00000159eff8ba40: 4c8d 74cc | f885 d20f | 8e09 0000 | 0068 0000 | 0000 ffca | 7ff7 5055 | 488b ec41 | 5568 0000 
  0x00000159eff8ba60: 0000 4c8b | 6b08 4d8d | 6d38 5348 | 8b53 0848 | 8b52 0848 | 8b52 1848 | 8b52 7048 | 8b12 5248 
  0x00000159eff8ba80: 8b53 1048 | 85d2 0f84 | 0700 0000 | 4881 c208 | 0100 0052 | 488b 5308 | 488b 5208 | 488b 5210 
  0x00000159eff8baa0: 5249 8bc6 | 482b c548 | c1e8 0350 | 4155 6800 | 0000 0048 | 8924 2441 | c687 7904 | 0000 0148 
  0x00000159eff8bac0: 8b45 d848 | 85c0 0f84 | 8800 0000 | 8b48 fc85 | c90f 887d | 0000 0048 | 03c1 488b | 4808 4883 
  0x00000159eff8bae0: e902 488b | 54c8 1048 | f7da 498b | 14d6 4885 | d275 10f6 | 44c8 1801 | 7554 f048 | 834c c818 
  0x00000159eff8bb00: 01eb 4b8b | 5208 48c1 | e203 4c8b | d248 3354 | c818 48f7 | c2fc ffff | ff74 33f6 | c202 752e 
  0x00000159eff8bb20: 4883 7cc8 | 1800 7421 | 4883 7cc8 | 1801 7419 | 498b d248 | 3354 c818 | 48f7 c2fc | ffff ff74 
  0x00000159eff8bb40: 0d48 834c | c818 02eb | 0548 8954 | c818 4883 | e902 798e | 488b 4310 | 4885 c074 | 208b 88cc 
  0x00000159eff8bb60: 0000 0083 | c102 8988 | cc00 0000 | 2388 e000 | 0000 0f84 | 9601 0000 | e9cf 0000 | 0048 8b43 
  0x00000159eff8bb80: 1848 85c0 | 0f85 b000 | 0000 e805 | 0000 00e9 | 9900 0000 | 488b d348 | 8d44 2408 | 4c89 6dc0 
  0x00000159eff8bba0: 498b cfc5 | f877 4989 | afa8 0300 | 0049 8987 | 9803 0000 | 4883 ec20 | 40f6 c40f | 0f84 1900 
  0x00000159eff8bbc0: 0000 4883 | ec08 48b8 | b028 3973 | fb7f 0000 | ffd0 4883 | c408 e90c | 0000 0048 | b8b0 2839 
  0x00000159eff8bbe0: 73fb 7f00 | 00ff d048 | 83c4 2049 | c787 9803 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x00000159eff8bc00: 0049 c787 | a003 0000 | 0000 0000 | c5f8 7749 | 837f 0800 | 0f84 0500 | 0000 e9e1 | 52ff ff4c 
  0x00000159eff8bc20: 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b43 | 1848 85c0 | 0f84 1200 | 0000 8b48 | 0883 c102 
  0x00000159eff8bc40: 8948 0823 | 481c 0f84 | c200 0000 | 493b a7e8 | 0400 000f | 8748 0000 | 0089 8424 | 00f0 ffff 
  0x00000159eff8bc60: 8984 2400 | e0ff ff89 | 8424 00d0 | ffff 8984 | 2400 c0ff | ff89 8424 | 00b0 ffff | 8984 2400 
  0x00000159eff8bc80: a0ff ff89 | 8424 0090 | ffff 8984 | 2400 80ff | ff49 3ba7 | e004 0000 | 7607 4989 | a7e8 0400 
  0x00000159eff8bca0: 0041 c687 | 7904 0000 | 0049 ba76 | ddc5 73fb | 7f00 0041 | 803a 000f | 843e 0000 | 0048 8b55 
  0x00000159eff8bcc0: e849 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 80a9 6f73 | fb7f 0000 
  0x00000159eff8bce0: ffd0 4883 | c408 e90c | 0000 0048 | b880 a96f | 73fb 7f00 | 00ff d048 | 83c4 2041 | 0fb6 5d00 
  0x00000159eff8bd00: 49ba 40b0 | c873 fb7f | 0000 41ff | 24da ba00 | 0000 00e8 | 0500 0000 | e996 0000 | 0048 8d44 
  0x00000159eff8bd20: 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 | ec20 40f6 
  0x00000159eff8bd40: c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 6033 | 3973 fb7f | 0000 ffd0 | 4883 c408 | e90c 0000 
  0x00000159eff8bd60: 0048 b860 | 3339 73fb | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 | 49c7 87a8 
  0x00000159eff8bd80: 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f | 0800 0f84 | 0500 0000 
  0x00000159eff8bda0: e95b 51ff | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c348 | 8b5d e8e9 | 90fe ffff | 0f1f 4000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000159fd6eb650, length=11, elements={
0x00000159e4828a50, 0x00000159fd523b60, 0x00000159fd5265d0, 0x00000159fd54aa40,
0x00000159fd54b4a0, 0x00000159fd54bf00, 0x00000159fd56cc00, 0x00000159fd528040,
0x00000159fd580af0, 0x00000159fd707790, 0x00000159fd709a60
}

Java Threads: ( => current thread )
=>0x00000159e4828a50 JavaThread "main"                              [_thread_in_vm, id=42364, stack(0x000000072fb00000,0x000000072fc00000) (1024K)]
  0x00000159fd523b60 JavaThread "Reference Handler"          daemon [_thread_blocked, id=34976, stack(0x0000000730300000,0x0000000730400000) (1024K)]
  0x00000159fd5265d0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=32492, stack(0x0000000730400000,0x0000000730500000) (1024K)]
  0x00000159fd54aa40 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=27700, stack(0x0000000730500000,0x0000000730600000) (1024K)]
  0x00000159fd54b4a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=18756, stack(0x0000000730600000,0x0000000730700000) (1024K)]
  0x00000159fd54bf00 JavaThread "Service Thread"             daemon [_thread_blocked, id=13612, stack(0x0000000730700000,0x0000000730800000) (1024K)]
  0x00000159fd56cc00 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=43068, stack(0x0000000730800000,0x0000000730900000) (1024K)]
  0x00000159fd528040 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=3380, stack(0x0000000730900000,0x0000000730a00000) (1024K)]
  0x00000159fd580af0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=44596, stack(0x0000000730a00000,0x0000000730b00000) (1024K)]
  0x00000159fd707790 JavaThread "Notification Thread"        daemon [_thread_blocked, id=26628, stack(0x0000000730b00000,0x0000000730c00000) (1024K)]
  0x00000159fd709a60 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=9396, stack(0x0000000730c00000,0x0000000730d00000) (1024K)]
Total: 11

Other Threads:
  0x00000159fd4c2830 VMThread "VM Thread"                           [id=30772, stack(0x0000000730200000,0x0000000730300000) (1024K)]
  0x00000159fa8ecca0 WatcherThread "VM Periodic Task Thread"        [id=34460, stack(0x0000000730100000,0x0000000730200000) (1024K)]
  0x00000159fa780980 WorkerThread "GC Thread#0"                     [id=44788, stack(0x000000072fc00000,0x000000072fd00000) (1024K)]
  0x00000159e489bff0 ConcurrentGCThread "G1 Main Marker"            [id=4076, stack(0x000000072fd00000,0x000000072fe00000) (1024K)]
  0x00000159e489d290 WorkerThread "G1 Conc#0"                       [id=20680, stack(0x000000072fe00000,0x000000072ff00000) (1024K)]
  0x00000159fa82d980 ConcurrentGCThread "G1 Refine#0"               [id=31748, stack(0x000000072ff00000,0x0000000730000000) (1024K)]
  0x00000159fa82e4f0 ConcurrentGCThread "G1 Service"                [id=25712, stack(0x0000000730000000,0x0000000730100000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffb73c74748] Metaspace_lock - owner thread: 0x00000159e4828a50

Heap address: 0x000000042d000000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x000000062a000000-0x000000066a000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x66a000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 0K [0x000000042d000000, 0x0000000629400000)
  region size 4096K, 1 young (4096K), 0 survivors (0K)
 Metaspace       used 5652K, committed 5760K, reserved 1114112K
  class space    used 457K, committed 512K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x000000042d000000, 0x000000042d000000, 0x000000042d400000|  0%| F|  |TAMS 0x000000042d000000| PB 0x000000042d000000| Untracked 
|   1|0x000000042d400000, 0x000000042d400000, 0x000000042d800000|  0%| F|  |TAMS 0x000000042d400000| PB 0x000000042d400000| Untracked 
|   2|0x000000042d800000, 0x000000042d800000, 0x000000042dc00000|  0%| F|  |TAMS 0x000000042d800000| PB 0x000000042d800000| Untracked 
|   3|0x000000042dc00000, 0x000000042dc00000, 0x000000042e000000|  0%| F|  |TAMS 0x000000042dc00000| PB 0x000000042dc00000| Untracked 
|   4|0x000000042e000000, 0x000000042e000000, 0x000000042e400000|  0%| F|  |TAMS 0x000000042e000000| PB 0x000000042e000000| Untracked 
|   5|0x000000042e400000, 0x000000042e400000, 0x000000042e800000|  0%| F|  |TAMS 0x000000042e400000| PB 0x000000042e400000| Untracked 
|   6|0x000000042e800000, 0x000000042e800000, 0x000000042ec00000|  0%| F|  |TAMS 0x000000042e800000| PB 0x000000042e800000| Untracked 
|   7|0x000000042ec00000, 0x000000042ec00000, 0x000000042f000000|  0%| F|  |TAMS 0x000000042ec00000| PB 0x000000042ec00000| Untracked 
|   8|0x000000042f000000, 0x000000042f000000, 0x000000042f400000|  0%| F|  |TAMS 0x000000042f000000| PB 0x000000042f000000| Untracked 
|   9|0x000000042f400000, 0x000000042f400000, 0x000000042f800000|  0%| F|  |TAMS 0x000000042f400000| PB 0x000000042f400000| Untracked 
|  10|0x000000042f800000, 0x000000042f800000, 0x000000042fc00000|  0%| F|  |TAMS 0x000000042f800000| PB 0x000000042f800000| Untracked 
|  11|0x000000042fc00000, 0x000000042fc00000, 0x0000000430000000|  0%| F|  |TAMS 0x000000042fc00000| PB 0x000000042fc00000| Untracked 
|  12|0x0000000430000000, 0x0000000430000000, 0x0000000430400000|  0%| F|  |TAMS 0x0000000430000000| PB 0x0000000430000000| Untracked 
|  13|0x0000000430400000, 0x0000000430400000, 0x0000000430800000|  0%| F|  |TAMS 0x0000000430400000| PB 0x0000000430400000| Untracked 
|  14|0x0000000430800000, 0x0000000430800000, 0x0000000430c00000|  0%| F|  |TAMS 0x0000000430800000| PB 0x0000000430800000| Untracked 
|  15|0x0000000430c00000, 0x0000000430c00000, 0x0000000431000000|  0%| F|  |TAMS 0x0000000430c00000| PB 0x0000000430c00000| Untracked 
|  16|0x0000000431000000, 0x0000000431000000, 0x0000000431400000|  0%| F|  |TAMS 0x0000000431000000| PB 0x0000000431000000| Untracked 
|  17|0x0000000431400000, 0x0000000431400000, 0x0000000431800000|  0%| F|  |TAMS 0x0000000431400000| PB 0x0000000431400000| Untracked 
|  18|0x0000000431800000, 0x0000000431800000, 0x0000000431c00000|  0%| F|  |TAMS 0x0000000431800000| PB 0x0000000431800000| Untracked 
|  19|0x0000000431c00000, 0x0000000431c00000, 0x0000000432000000|  0%| F|  |TAMS 0x0000000431c00000| PB 0x0000000431c00000| Untracked 
|  20|0x0000000432000000, 0x0000000432000000, 0x0000000432400000|  0%| F|  |TAMS 0x0000000432000000| PB 0x0000000432000000| Untracked 
|  21|0x0000000432400000, 0x0000000432400000, 0x0000000432800000|  0%| F|  |TAMS 0x0000000432400000| PB 0x0000000432400000| Untracked 
|  22|0x0000000432800000, 0x0000000432800000, 0x0000000432c00000|  0%| F|  |TAMS 0x0000000432800000| PB 0x0000000432800000| Untracked 
|  23|0x0000000432c00000, 0x0000000432c00000, 0x0000000433000000|  0%| F|  |TAMS 0x0000000432c00000| PB 0x0000000432c00000| Untracked 
|  24|0x0000000433000000, 0x0000000433000000, 0x0000000433400000|  0%| F|  |TAMS 0x0000000433000000| PB 0x0000000433000000| Untracked 
|  25|0x0000000433400000, 0x0000000433400000, 0x0000000433800000|  0%| F|  |TAMS 0x0000000433400000| PB 0x0000000433400000| Untracked 
|  26|0x0000000433800000, 0x0000000433800000, 0x0000000433c00000|  0%| F|  |TAMS 0x0000000433800000| PB 0x0000000433800000| Untracked 
|  27|0x0000000433c00000, 0x0000000433c00000, 0x0000000434000000|  0%| F|  |TAMS 0x0000000433c00000| PB 0x0000000433c00000| Untracked 
|  28|0x0000000434000000, 0x0000000434000000, 0x0000000434400000|  0%| F|  |TAMS 0x0000000434000000| PB 0x0000000434000000| Untracked 
|  29|0x0000000434400000, 0x0000000434400000, 0x0000000434800000|  0%| F|  |TAMS 0x0000000434400000| PB 0x0000000434400000| Untracked 
|  30|0x0000000434800000, 0x0000000434800000, 0x0000000434c00000|  0%| F|  |TAMS 0x0000000434800000| PB 0x0000000434800000| Untracked 
|  31|0x0000000434c00000, 0x0000000434c00000, 0x0000000435000000|  0%| F|  |TAMS 0x0000000434c00000| PB 0x0000000434c00000| Untracked 
|  32|0x0000000435000000, 0x0000000435000000, 0x0000000435400000|  0%| F|  |TAMS 0x0000000435000000| PB 0x0000000435000000| Untracked 
|  33|0x0000000435400000, 0x0000000435400000, 0x0000000435800000|  0%| F|  |TAMS 0x0000000435400000| PB 0x0000000435400000| Untracked 
|  34|0x0000000435800000, 0x0000000435800000, 0x0000000435c00000|  0%| F|  |TAMS 0x0000000435800000| PB 0x0000000435800000| Untracked 
|  35|0x0000000435c00000, 0x0000000435c00000, 0x0000000436000000|  0%| F|  |TAMS 0x0000000435c00000| PB 0x0000000435c00000| Untracked 
|  36|0x0000000436000000, 0x0000000436000000, 0x0000000436400000|  0%| F|  |TAMS 0x0000000436000000| PB 0x0000000436000000| Untracked 
|  37|0x0000000436400000, 0x0000000436400000, 0x0000000436800000|  0%| F|  |TAMS 0x0000000436400000| PB 0x0000000436400000| Untracked 
|  38|0x0000000436800000, 0x0000000436800000, 0x0000000436c00000|  0%| F|  |TAMS 0x0000000436800000| PB 0x0000000436800000| Untracked 
|  39|0x0000000436c00000, 0x0000000436c00000, 0x0000000437000000|  0%| F|  |TAMS 0x0000000436c00000| PB 0x0000000436c00000| Untracked 
|  40|0x0000000437000000, 0x0000000437000000, 0x0000000437400000|  0%| F|  |TAMS 0x0000000437000000| PB 0x0000000437000000| Untracked 
|  41|0x0000000437400000, 0x0000000437400000, 0x0000000437800000|  0%| F|  |TAMS 0x0000000437400000| PB 0x0000000437400000| Untracked 
|  42|0x0000000437800000, 0x0000000437800000, 0x0000000437c00000|  0%| F|  |TAMS 0x0000000437800000| PB 0x0000000437800000| Untracked 
|  43|0x0000000437c00000, 0x0000000437c00000, 0x0000000438000000|  0%| F|  |TAMS 0x0000000437c00000| PB 0x0000000437c00000| Untracked 
|  44|0x0000000438000000, 0x0000000438000000, 0x0000000438400000|  0%| F|  |TAMS 0x0000000438000000| PB 0x0000000438000000| Untracked 
|  45|0x0000000438400000, 0x0000000438400000, 0x0000000438800000|  0%| F|  |TAMS 0x0000000438400000| PB 0x0000000438400000| Untracked 
|  46|0x0000000438800000, 0x0000000438800000, 0x0000000438c00000|  0%| F|  |TAMS 0x0000000438800000| PB 0x0000000438800000| Untracked 
|  47|0x0000000438c00000, 0x0000000438c00000, 0x0000000439000000|  0%| F|  |TAMS 0x0000000438c00000| PB 0x0000000438c00000| Untracked 
|  48|0x0000000439000000, 0x0000000439000000, 0x0000000439400000|  0%| F|  |TAMS 0x0000000439000000| PB 0x0000000439000000| Untracked 
|  49|0x0000000439400000, 0x0000000439400000, 0x0000000439800000|  0%| F|  |TAMS 0x0000000439400000| PB 0x0000000439400000| Untracked 
|  50|0x0000000439800000, 0x0000000439800000, 0x0000000439c00000|  0%| F|  |TAMS 0x0000000439800000| PB 0x0000000439800000| Untracked 
|  51|0x0000000439c00000, 0x0000000439c00000, 0x000000043a000000|  0%| F|  |TAMS 0x0000000439c00000| PB 0x0000000439c00000| Untracked 
|  52|0x000000043a000000, 0x000000043a000000, 0x000000043a400000|  0%| F|  |TAMS 0x000000043a000000| PB 0x000000043a000000| Untracked 
|  53|0x000000043a400000, 0x000000043a400000, 0x000000043a800000|  0%| F|  |TAMS 0x000000043a400000| PB 0x000000043a400000| Untracked 
|  54|0x000000043a800000, 0x000000043a800000, 0x000000043ac00000|  0%| F|  |TAMS 0x000000043a800000| PB 0x000000043a800000| Untracked 
|  55|0x000000043ac00000, 0x000000043ac00000, 0x000000043b000000|  0%| F|  |TAMS 0x000000043ac00000| PB 0x000000043ac00000| Untracked 
|  56|0x000000043b000000, 0x000000043b000000, 0x000000043b400000|  0%| F|  |TAMS 0x000000043b000000| PB 0x000000043b000000| Untracked 
|  57|0x000000043b400000, 0x000000043b400000, 0x000000043b800000|  0%| F|  |TAMS 0x000000043b400000| PB 0x000000043b400000| Untracked 
|  58|0x000000043b800000, 0x000000043b800000, 0x000000043bc00000|  0%| F|  |TAMS 0x000000043b800000| PB 0x000000043b800000| Untracked 
|  59|0x000000043bc00000, 0x000000043bc00000, 0x000000043c000000|  0%| F|  |TAMS 0x000000043bc00000| PB 0x000000043bc00000| Untracked 
|  60|0x000000043c000000, 0x000000043c000000, 0x000000043c400000|  0%| F|  |TAMS 0x000000043c000000| PB 0x000000043c000000| Untracked 
|  61|0x000000043c400000, 0x000000043c400000, 0x000000043c800000|  0%| F|  |TAMS 0x000000043c400000| PB 0x000000043c400000| Untracked 
|  62|0x000000043c800000, 0x000000043c800000, 0x000000043cc00000|  0%| F|  |TAMS 0x000000043c800000| PB 0x000000043c800000| Untracked 
|  63|0x000000043cc00000, 0x000000043cc00000, 0x000000043d000000|  0%| F|  |TAMS 0x000000043cc00000| PB 0x000000043cc00000| Untracked 
|  64|0x000000043d000000, 0x000000043d000000, 0x000000043d400000|  0%| F|  |TAMS 0x000000043d000000| PB 0x000000043d000000| Untracked 
|  65|0x000000043d400000, 0x000000043d400000, 0x000000043d800000|  0%| F|  |TAMS 0x000000043d400000| PB 0x000000043d400000| Untracked 
|  66|0x000000043d800000, 0x000000043d800000, 0x000000043dc00000|  0%| F|  |TAMS 0x000000043d800000| PB 0x000000043d800000| Untracked 
|  67|0x000000043dc00000, 0x000000043dc00000, 0x000000043e000000|  0%| F|  |TAMS 0x000000043dc00000| PB 0x000000043dc00000| Untracked 
|  68|0x000000043e000000, 0x000000043e000000, 0x000000043e400000|  0%| F|  |TAMS 0x000000043e000000| PB 0x000000043e000000| Untracked 
|  69|0x000000043e400000, 0x000000043e400000, 0x000000043e800000|  0%| F|  |TAMS 0x000000043e400000| PB 0x000000043e400000| Untracked 
|  70|0x000000043e800000, 0x000000043e800000, 0x000000043ec00000|  0%| F|  |TAMS 0x000000043e800000| PB 0x000000043e800000| Untracked 
|  71|0x000000043ec00000, 0x000000043ec00000, 0x000000043f000000|  0%| F|  |TAMS 0x000000043ec00000| PB 0x000000043ec00000| Untracked 
|  72|0x000000043f000000, 0x000000043f000000, 0x000000043f400000|  0%| F|  |TAMS 0x000000043f000000| PB 0x000000043f000000| Untracked 
|  73|0x000000043f400000, 0x000000043f400000, 0x000000043f800000|  0%| F|  |TAMS 0x000000043f400000| PB 0x000000043f400000| Untracked 
|  74|0x000000043f800000, 0x000000043f800000, 0x000000043fc00000|  0%| F|  |TAMS 0x000000043f800000| PB 0x000000043f800000| Untracked 
|  75|0x000000043fc00000, 0x000000043fc00000, 0x0000000440000000|  0%| F|  |TAMS 0x000000043fc00000| PB 0x000000043fc00000| Untracked 
|  76|0x0000000440000000, 0x0000000440000000, 0x0000000440400000|  0%| F|  |TAMS 0x0000000440000000| PB 0x0000000440000000| Untracked 
|  77|0x0000000440400000, 0x0000000440400000, 0x0000000440800000|  0%| F|  |TAMS 0x0000000440400000| PB 0x0000000440400000| Untracked 
|  78|0x0000000440800000, 0x0000000440800000, 0x0000000440c00000|  0%| F|  |TAMS 0x0000000440800000| PB 0x0000000440800000| Untracked 
|  79|0x0000000440c00000, 0x0000000440c00000, 0x0000000441000000|  0%| F|  |TAMS 0x0000000440c00000| PB 0x0000000440c00000| Untracked 
|  80|0x0000000441000000, 0x0000000441000000, 0x0000000441400000|  0%| F|  |TAMS 0x0000000441000000| PB 0x0000000441000000| Untracked 
|  81|0x0000000441400000, 0x0000000441400000, 0x0000000441800000|  0%| F|  |TAMS 0x0000000441400000| PB 0x0000000441400000| Untracked 
|  82|0x0000000441800000, 0x0000000441800000, 0x0000000441c00000|  0%| F|  |TAMS 0x0000000441800000| PB 0x0000000441800000| Untracked 
|  83|0x0000000441c00000, 0x0000000441c00000, 0x0000000442000000|  0%| F|  |TAMS 0x0000000441c00000| PB 0x0000000441c00000| Untracked 
|  84|0x0000000442000000, 0x0000000442000000, 0x0000000442400000|  0%| F|  |TAMS 0x0000000442000000| PB 0x0000000442000000| Untracked 
|  85|0x0000000442400000, 0x0000000442400000, 0x0000000442800000|  0%| F|  |TAMS 0x0000000442400000| PB 0x0000000442400000| Untracked 
|  86|0x0000000442800000, 0x0000000442800000, 0x0000000442c00000|  0%| F|  |TAMS 0x0000000442800000| PB 0x0000000442800000| Untracked 
|  87|0x0000000442c00000, 0x0000000442c00000, 0x0000000443000000|  0%| F|  |TAMS 0x0000000442c00000| PB 0x0000000442c00000| Untracked 
|  88|0x0000000443000000, 0x0000000443000000, 0x0000000443400000|  0%| F|  |TAMS 0x0000000443000000| PB 0x0000000443000000| Untracked 
|  89|0x0000000443400000, 0x0000000443400000, 0x0000000443800000|  0%| F|  |TAMS 0x0000000443400000| PB 0x0000000443400000| Untracked 
|  90|0x0000000443800000, 0x0000000443800000, 0x0000000443c00000|  0%| F|  |TAMS 0x0000000443800000| PB 0x0000000443800000| Untracked 
|  91|0x0000000443c00000, 0x0000000443c00000, 0x0000000444000000|  0%| F|  |TAMS 0x0000000443c00000| PB 0x0000000443c00000| Untracked 
|  92|0x0000000444000000, 0x0000000444000000, 0x0000000444400000|  0%| F|  |TAMS 0x0000000444000000| PB 0x0000000444000000| Untracked 
|  93|0x0000000444400000, 0x0000000444400000, 0x0000000444800000|  0%| F|  |TAMS 0x0000000444400000| PB 0x0000000444400000| Untracked 
|  94|0x0000000444800000, 0x0000000444800000, 0x0000000444c00000|  0%| F|  |TAMS 0x0000000444800000| PB 0x0000000444800000| Untracked 
|  95|0x0000000444c00000, 0x0000000444c00000, 0x0000000445000000|  0%| F|  |TAMS 0x0000000444c00000| PB 0x0000000444c00000| Untracked 
|  96|0x0000000445000000, 0x0000000445000000, 0x0000000445400000|  0%| F|  |TAMS 0x0000000445000000| PB 0x0000000445000000| Untracked 
|  97|0x0000000445400000, 0x0000000445400000, 0x0000000445800000|  0%| F|  |TAMS 0x0000000445400000| PB 0x0000000445400000| Untracked 
|  98|0x0000000445800000, 0x0000000445800000, 0x0000000445c00000|  0%| F|  |TAMS 0x0000000445800000| PB 0x0000000445800000| Untracked 
|  99|0x0000000445c00000, 0x0000000445c00000, 0x0000000446000000|  0%| F|  |TAMS 0x0000000445c00000| PB 0x0000000445c00000| Untracked 
| 100|0x0000000446000000, 0x0000000446000000, 0x0000000446400000|  0%| F|  |TAMS 0x0000000446000000| PB 0x0000000446000000| Untracked 
| 101|0x0000000446400000, 0x0000000446400000, 0x0000000446800000|  0%| F|  |TAMS 0x0000000446400000| PB 0x0000000446400000| Untracked 
| 102|0x0000000446800000, 0x0000000446800000, 0x0000000446c00000|  0%| F|  |TAMS 0x0000000446800000| PB 0x0000000446800000| Untracked 
| 103|0x0000000446c00000, 0x0000000446c00000, 0x0000000447000000|  0%| F|  |TAMS 0x0000000446c00000| PB 0x0000000446c00000| Untracked 
| 104|0x0000000447000000, 0x0000000447000000, 0x0000000447400000|  0%| F|  |TAMS 0x0000000447000000| PB 0x0000000447000000| Untracked 
| 105|0x0000000447400000, 0x0000000447400000, 0x0000000447800000|  0%| F|  |TAMS 0x0000000447400000| PB 0x0000000447400000| Untracked 
| 106|0x0000000447800000, 0x0000000447800000, 0x0000000447c00000|  0%| F|  |TAMS 0x0000000447800000| PB 0x0000000447800000| Untracked 
| 107|0x0000000447c00000, 0x0000000447c00000, 0x0000000448000000|  0%| F|  |TAMS 0x0000000447c00000| PB 0x0000000447c00000| Untracked 
| 108|0x0000000448000000, 0x0000000448000000, 0x0000000448400000|  0%| F|  |TAMS 0x0000000448000000| PB 0x0000000448000000| Untracked 
| 109|0x0000000448400000, 0x0000000448400000, 0x0000000448800000|  0%| F|  |TAMS 0x0000000448400000| PB 0x0000000448400000| Untracked 
| 110|0x0000000448800000, 0x0000000448800000, 0x0000000448c00000|  0%| F|  |TAMS 0x0000000448800000| PB 0x0000000448800000| Untracked 
| 111|0x0000000448c00000, 0x0000000448c00000, 0x0000000449000000|  0%| F|  |TAMS 0x0000000448c00000| PB 0x0000000448c00000| Untracked 
| 112|0x0000000449000000, 0x0000000449000000, 0x0000000449400000|  0%| F|  |TAMS 0x0000000449000000| PB 0x0000000449000000| Untracked 
| 113|0x0000000449400000, 0x0000000449400000, 0x0000000449800000|  0%| F|  |TAMS 0x0000000449400000| PB 0x0000000449400000| Untracked 
| 114|0x0000000449800000, 0x0000000449800000, 0x0000000449c00000|  0%| F|  |TAMS 0x0000000449800000| PB 0x0000000449800000| Untracked 
| 115|0x0000000449c00000, 0x0000000449c00000, 0x000000044a000000|  0%| F|  |TAMS 0x0000000449c00000| PB 0x0000000449c00000| Untracked 
| 116|0x000000044a000000, 0x000000044a000000, 0x000000044a400000|  0%| F|  |TAMS 0x000000044a000000| PB 0x000000044a000000| Untracked 
| 117|0x000000044a400000, 0x000000044a400000, 0x000000044a800000|  0%| F|  |TAMS 0x000000044a400000| PB 0x000000044a400000| Untracked 
| 118|0x000000044a800000, 0x000000044a800000, 0x000000044ac00000|  0%| F|  |TAMS 0x000000044a800000| PB 0x000000044a800000| Untracked 
| 119|0x000000044ac00000, 0x000000044ac00000, 0x000000044b000000|  0%| F|  |TAMS 0x000000044ac00000| PB 0x000000044ac00000| Untracked 
| 120|0x000000044b000000, 0x000000044b000000, 0x000000044b400000|  0%| F|  |TAMS 0x000000044b000000| PB 0x000000044b000000| Untracked 
| 121|0x000000044b400000, 0x000000044b400000, 0x000000044b800000|  0%| F|  |TAMS 0x000000044b400000| PB 0x000000044b400000| Untracked 
| 122|0x000000044b800000, 0x000000044b800000, 0x000000044bc00000|  0%| F|  |TAMS 0x000000044b800000| PB 0x000000044b800000| Untracked 
| 123|0x000000044bc00000, 0x000000044bc00000, 0x000000044c000000|  0%| F|  |TAMS 0x000000044bc00000| PB 0x000000044bc00000| Untracked 
| 124|0x000000044c000000, 0x000000044c000000, 0x000000044c400000|  0%| F|  |TAMS 0x000000044c000000| PB 0x000000044c000000| Untracked 
| 125|0x000000044c400000, 0x000000044c400000, 0x000000044c800000|  0%| F|  |TAMS 0x000000044c400000| PB 0x000000044c400000| Untracked 
| 126|0x000000044c800000, 0x000000044c800000, 0x000000044cc00000|  0%| F|  |TAMS 0x000000044c800000| PB 0x000000044c800000| Untracked 
| 127|0x000000044cc00000, 0x000000044cee16b8, 0x000000044d000000| 72%| E|  |TAMS 0x000000044cc00000| PB 0x000000044cc00000| Complete 

Card table byte_map: [0x00000159f96c0000,0x00000159fa6b0000] _byte_map_base: 0x00000159f7558000

Marking Bits: (CMBitMap*) 0x00000159e488b750
 Bits: [0x0000015980000000, 0x0000015987f10000)

Polling page: 0x00000159e2f50000

Metaspace:

Usage:
  Non-class:      5.07 MB used.
      Class:    457.56 KB used.
       Both:      5.52 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       5.12 MB (  8%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     512.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.62 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.49 MB
       Class:  15.53 MB
        Both:  26.02 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 4.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 90.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 11.
num_chunk_merges: 0.
num_chunk_splits: 5.
num_chunks_enlarged: 1.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=15Kb max_used=15Kb free=119985Kb
 bounds [0x00000159f0520000, 0x00000159f0790000, 0x00000159f7a50000]
CodeHeap 'profiled nmethods': size=120000Kb used=144Kb max_used=144Kb free=119855Kb
 bounds [0x00000159e8a50000, 0x00000159e8cc0000, 0x00000159eff80000]
CodeHeap 'non-nmethods': size=5760Kb used=1206Kb max_used=1208Kb free=4554Kb
 bounds [0x00000159eff80000, 0x00000159f01f0000, 0x00000159f0520000]
 total_blobs=436 nmethods=101 adapters=240
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.095 Thread 0x00000159fd580af0 nmethod 86 0x00000159e8a6c290 code [0x00000159e8a6c480, 0x00000159e8a6c9a0]
Event: 0.095 Thread 0x00000159fd580af0   88       3       java.lang.StringUTF16::compress (59 bytes)
Event: 0.095 Thread 0x00000159fd580af0 nmethod 88 0x00000159e8a6cb90 code [0x00000159e8a6cd80, 0x00000159e8a6d208]
Event: 0.095 Thread 0x00000159fd580af0   91       3       java.util.HashMap::putVal (300 bytes)
Event: 0.096 Thread 0x00000159fd580af0 nmethod 91 0x00000159e8a6d490 code [0x00000159e8a6d740, 0x00000159e8a6e8d8]
Event: 0.096 Thread 0x00000159fd580af0   92       3       java.util.HashMap::put (13 bytes)
Event: 0.096 Thread 0x00000159fd580af0 nmethod 92 0x00000159e8a6ee10 code [0x00000159e8a6efe0, 0x00000159e8a6f2e0]
Event: 0.096 Thread 0x00000159fd580af0   90       3       java.lang.String::checkBoundsBeginEnd (11 bytes)
Event: 0.096 Thread 0x00000159fd580af0 nmethod 90 0x00000159e8a6f410 code [0x00000159e8a6f5c0, 0x00000159e8a6f800]
Event: 0.096 Thread 0x00000159fd580af0   89       3       java.lang.String::<init> (15 bytes)
Event: 0.096 Thread 0x00000159fd580af0 nmethod 89 0x00000159e8a6f910 code [0x00000159e8a6faa0, 0x00000159e8a6fc70]
Event: 0.097 Thread 0x00000159fd580af0   93       3       java.lang.CharacterData::of (136 bytes)
Event: 0.097 Thread 0x00000159fd580af0 nmethod 93 0x00000159e8a6fd10 code [0x00000159e8a6ff60, 0x00000159e8a708e0]
Event: 0.097 Thread 0x00000159fd580af0   95       3       java.lang.Character::toUpperCase (9 bytes)
Event: 0.097 Thread 0x00000159fd580af0 nmethod 95 0x00000159e8a70b10 code [0x00000159e8a70cc0, 0x00000159e8a70eb0]
Event: 0.097 Thread 0x00000159fd580af0   96       3       java.lang.CharacterDataLatin1::toUpperCase (67 bytes)
Event: 0.097 Thread 0x00000159fd580af0 nmethod 96 0x00000159e8a70f90 code [0x00000159e8a71160, 0x00000159e8a714b0]
Event: 0.097 Thread 0x00000159fd580af0   97       3       java.util.TreeMap::parentOf (13 bytes)
Event: 0.097 Thread 0x00000159fd580af0 nmethod 97 0x00000159e8a71510 code [0x00000159e8a716c0, 0x00000159e8a71830]
Event: 0.097 Thread 0x00000159fd580af0  101       3       java.lang.ProcessEnvironment$NameComparator::compare (90 bytes)

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.014 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.095 Loading class java/lang/ProcessEnvironment$NameComparator
Event: 0.095 Loading class java/lang/ProcessEnvironment$NameComparator done
Event: 0.095 Loading class java/lang/ProcessEnvironment$EntryComparator
Event: 0.095 Loading class java/lang/ProcessEnvironment$EntryComparator done
Event: 0.095 Loading class java/util/TreeMap
Event: 0.096 Loading class java/util/NavigableMap
Event: 0.096 Loading class java/util/SortedMap
Event: 0.096 Loading class java/util/SequencedMap
Event: 0.096 Loading class java/util/SequencedMap done
Event: 0.096 Loading class java/util/SortedMap done
Event: 0.096 Loading class java/util/NavigableMap done
Event: 0.096 Loading class java/util/TreeMap done
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntrySet
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntrySet done
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntrySet$1
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntrySet$1 done
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntry
Event: 0.096 Loading class java/lang/ProcessEnvironment$CheckedEntry done
Event: 0.096 Loading class java/util/TreeMap$Entry
Event: 0.097 Loading class java/util/TreeMap$Entry done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (2 events):
Event: 0.095 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.095 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (14 events):
Event: 0.013 Thread 0x00000159e4828a50 Thread added: 0x00000159e4828a50
Event: 0.052 Thread 0x00000159e4828a50 Thread added: 0x00000159fd523b60
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd5265d0
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd54aa40
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd54b4a0
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd54bf00
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd56cc00
Event: 0.053 Thread 0x00000159e4828a50 Thread added: 0x00000159fd528040
Event: 0.054 Thread 0x00000159e4828a50 Thread added: 0x00000159fd580af0
Event: 0.074 Thread 0x00000159e4828a50 Thread added: 0x00000159fd707790
Event: 0.078 Thread 0x00000159e4828a50 Thread added: 0x00000159fd709a60
Event: 0.083 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.085 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.091 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll


Dynamic libraries:
0x00007ff6fdce0000 - 0x00007ff6fdcea000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffc144d0000 - 0x00007ffc146e7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffc13b50000 - 0x00007ffc13c14000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffc11850000 - 0x00007ffc11c09000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffc11ec0000 - 0x00007ffc11fd1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffbe2370000 - 0x00007ffbe238b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffbca0d0000 - 0x00007ffbca0e8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffc13980000 - 0x00007ffc13b2e000 	C:\WINDOWS\System32\USER32.dll
0x00007ffc11c10000 - 0x00007ffc11c36000 	C:\WINDOWS\System32\win32u.dll
0x00007ffc13da0000 - 0x00007ffc13dc9000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffc11ce0000 - 0x00007ffc11dfb000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffc12150000 - 0x00007ffc121ea000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffbe48f0000 - 0x00007ffbe4b82000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96\COMCTL32.dll
0x00007ffc12970000 - 0x00007ffc12a17000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffc12800000 - 0x00007ffc12831000 	C:\WINDOWS\System32\IMM32.DLL
0x0000000067d70000 - 0x0000000067d7d000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffc138c0000 - 0x00007ffc13972000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffc12590000 - 0x00007ffc12637000 	C:\WINDOWS\System32\sechost.dll
0x00007ffc11cb0000 - 0x00007ffc11cd8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffc13c80000 - 0x00007ffc13d94000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffbfd3e0000 - 0x00007ffbfd4e3000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffc12a80000 - 0x00007ffc132f6000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffc12a20000 - 0x00007ffc12a7e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffc11160000 - 0x00007ffc1116a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffbe2390000 - 0x00007ffbe239c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffbb2e70000 - 0x00007ffbb2efd000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffb72fb0000 - 0x00007ffb73d67000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffc12300000 - 0x00007ffc12371000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffc116a0000 - 0x00007ffc116ed000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffc080f0000 - 0x00007ffc08124000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffc11680000 - 0x00007ffc11693000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffc10780000 - 0x00007ffc10798000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffbdd0d0000 - 0x00007ffbdd0da000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffc0f210000 - 0x00007ffc0f442000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffc13fa0000 - 0x00007ffc1432f000 	C:\WINDOWS\System32\combase.dll
0x00007ffc13360000 - 0x00007ffc13437000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffbfa7a0000 - 0x00007ffbfa7d2000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffc117d0000 - 0x00007ffc1184b000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffbca0b0000 - 0x00007ffbca0cf000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ffbc9d90000 - 0x00007ffbc9da8000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffc0f680000 - 0x00007ffc0ff83000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffc0f540000 - 0x00007ffc0f67f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffc13df0000 - 0x00007ffc13ee9000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffc11700000 - 0x00007ffc1172b000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffbdd0b0000 - 0x00007ffbdd0c0000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffc0ba50000 - 0x00007ffc0bb86000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffc10c00000 - 0x00007ffc10c69000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffbc9d70000 - 0x00007ffbc9d86000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4391_none_2715d37f73803e96;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 9, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 37436K (0% of 33293192K total physical memory with 8931244K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 5640K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 12808B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4391)
OS uptime: 7 days 5:02 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (8721M free)
TotalPageFile size 42752M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 36M, peak: 36M
current process commit charge ("private bytes"): 600M, peak: 601M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
