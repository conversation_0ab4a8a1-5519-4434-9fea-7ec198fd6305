#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=45864, tid=16708
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.4+13-509.17-jcef (21.0.4+13) (build 21.0.4+13-b509.17)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.4+13-509.17-jcef (21.0.4+13-b509.17, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 

Host: Intel(R) Core(TM) i5-10500 CPU @ 3.10GHz, 12 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
Time: Thu Nov 21 10:49:40 2024  Windows 11 , 64 bit Build 22621 (10.0.22621.3958) elapsed time: 0.390710 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000175dd446ba0):  JavaThread "HttpClient-1-Worker-0" daemon [_thread_in_vm, id=16708, stack(0x00000062bc900000,0x00000062bca00000) (1024K)]

Stack: [0x00000062bc900000,0x00000062bca00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e36d9]
V  [jvm.dll+0x8c0bd3]
V  [jvm.dll+0x8c312e]
V  [jvm.dll+0x8c3813]
V  [jvm.dll+0x288256]
V  [jvm.dll+0x8bba1e]
V  [jvm.dll+0x684b95]
V  [jvm.dll+0x684bfa]
V  [jvm.dll+0x687416]
V  [jvm.dll+0x6872e2]
V  [jvm.dll+0x68554e]
V  [jvm.dll+0x2848e6]
V  [jvm.dll+0x73a39a]
V  [jvm.dll+0x73aedf]
V  [jvm.dll+0x3d10b0]
V  [jvm.dll+0x69eee7]
V  [jvm.dll+0x837664]
V  [jvm.dll+0x276d7b]
V  [jvm.dll+0x2735e7]
V  [jvm.dll+0x147124]
V  [jvm.dll+0x147467]
V  [jvm.dll+0x836d92]
V  [jvm.dll+0x5e8eb9]
V  [jvm.dll+0x5e8d00]
V  [jvm.dll+0x3e5632]
C  0x00000175c31da5d2

The last pc belongs to invokedynamic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  sun.security.ec.XDHKeyPairGenerator.initialize(Ljava/security/spec/AlgorithmParameterSpec;Ljava/security/SecureRandom;)V+0 jdk.crypto.ec@21.0.4
j  sun.security.ec.XDHKeyPairGenerator.tryInitialize(Ljava/security/spec/NamedParameterSpec;)V+3 jdk.crypto.ec@21.0.4
j  sun.security.ec.XDHKeyPairGenerator.<init>()V+23 jdk.crypto.ec@21.0.4
j  sun.security.ec.SunEC$ProviderService.newInstance(Ljava/lang/Object;)Ljava/lang/Object;+630 jdk.crypto.ec@21.0.4
j  sun.security.jca.GetInstance.getInstance(Ljava/security/Provider$Service;Ljava/lang/Class;)Lsun/security/jca/GetInstance$Instance;+2 java.base@21.0.4
j  java.security.KeyPairGenerator.getInstance(Ljava/lang/String;)Ljava/security/KeyPairGenerator;+74 java.base@21.0.4
j  sun.security.ssl.XDHKeyExchange$XDHEPossession.<init>(Lsun/security/ssl/NamedGroup;Ljava/security/SecureRandom;)V+8 java.base@21.0.4
j  sun.security.ssl.NamedGroup$XDHScheme.createPossession(Lsun/security/ssl/NamedGroup;Ljava/security/SecureRandom;)Lsun/security/ssl/SSLPossession;+6 java.base@21.0.4
j  sun.security.ssl.NamedGroup$NamedGroupSpec.createPossession(Lsun/security/ssl/NamedGroup;Ljava/security/SecureRandom;)Lsun/security/ssl/SSLPossession;+13 java.base@21.0.4
j  sun.security.ssl.NamedGroup.createPossession(Ljava/security/SecureRandom;)Lsun/security/ssl/SSLPossession;+6 java.base@21.0.4
j  sun.security.ssl.SSLKeyExchange$T13KeyAgreement.createPossession(Lsun/security/ssl/HandshakeContext;)Lsun/security/ssl/SSLPossession;+11 java.base@21.0.4
j  sun.security.ssl.SSLKeyExchange.createPossessions(Lsun/security/ssl/HandshakeContext;)[Lsun/security/ssl/SSLPossession;+198 java.base@21.0.4
j  sun.security.ssl.KeyShareExtension$CHKeyShareProducer.getShare(Lsun/security/ssl/ClientHandshakeContext;Lsun/security/ssl/NamedGroup;)[B+57 java.base@21.0.4
j  sun.security.ssl.KeyShareExtension$CHKeyShareProducer.produce(Lsun/security/ssl/ConnectionContext;Lsun/security/ssl/SSLHandshake$HandshakeMessage;)[B+171 java.base@21.0.4
j  sun.security.ssl.SSLExtension.produce(Lsun/security/ssl/ConnectionContext;Lsun/security/ssl/SSLHandshake$HandshakeMessage;)[B+13 java.base@21.0.4
j  sun.security.ssl.SSLExtensions.produce(Lsun/security/ssl/HandshakeContext;[Lsun/security/ssl/SSLExtension;)V+145 java.base@21.0.4
j  sun.security.ssl.ClientHello$ClientHelloKickstartProducer.produce(Lsun/security/ssl/ConnectionContext;)[B+962 java.base@21.0.4
j  sun.security.ssl.SSLHandshake.kickstart(Lsun/security/ssl/HandshakeContext;)V+47 java.base@21.0.4
j  sun.security.ssl.ClientHandshakeContext.kickstart()V+9 java.base@21.0.4
j  sun.security.ssl.TransportContext.kickstart()V+234 java.base@21.0.4
j  sun.security.ssl.SSLEngineImpl.writeRecord([Ljava/nio/ByteBuffer;II[Ljava/nio/ByteBuffer;II)Ljavax/net/ssl/SSLEngineResult;+117 java.base@21.0.4
j  sun.security.ssl.SSLEngineImpl.wrap([Ljava/nio/ByteBuffer;II[Ljava/nio/ByteBuffer;II)Ljavax/net/ssl/SSLEngineResult;+53 java.base@21.0.4
j  sun.security.ssl.SSLEngineImpl.wrap([Ljava/nio/ByteBuffer;IILjava/nio/ByteBuffer;)Ljavax/net/ssl/SSLEngineResult;+15 java.base@21.0.4
j  javax.net.ssl.SSLEngine.wrap([Ljava/nio/ByteBuffer;Ljava/nio/ByteBuffer;)Ljavax/net/ssl/SSLEngineResult;+20 java.base@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate$Writer.wrapBuffers([Ljava/nio/ByteBuffer;)Ljdk/internal/net/http/common/SSLFlowDelegate$EngineResult;+131 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate$Writer.processData()V+101 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate$Writer$WriterDownstreamPusher.run()V+4 java.net.http@21.0.4
j  jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(Ljdk/internal/net/http/common/SequentialScheduler$DeferredCompleter;)V+1 java.net.http@21.0.4
j  jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run()V+14 java.net.http@21.0.4
j  jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(Ljdk/internal/net/http/common/SequentialScheduler$SchedulableTask;Ljava/util/concurrent/Executor;)V+86 java.net.http@21.0.4
j  jdk.internal.net.http.common.SequentialScheduler.runOrSchedule()V+6 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate$Writer.incoming(Ljava/util/List;Z)V+155 java.net.http@21.0.4
j  jdk.internal.net.http.common.SubscriberWrapper.incomingCaller(Ljava/util/List;Z)V+3 java.net.http@21.0.4
j  jdk.internal.net.http.common.SubscriberWrapper.addData(Ljava/nio/ByteBuffer;)V+24 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate$Writer.onSubscribe()V+27 java.net.http@21.0.4
j  jdk.internal.net.http.common.SubscriberWrapper.onSubscribe(Ljava/util/concurrent/Flow$Subscription;)V+78 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLTube$SSLTubeFlowDelegate.connect(Ljava/util/concurrent/Flow$Subscriber;Ljava/util/concurrent/Flow$Subscriber;)V+95 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLFlowDelegate.<init>(Ljavax/net/ssl/SSLEngine;Ljava/util/concurrent/Executor;Ljava/util/function/Consumer;Ljava/util/concurrent/Flow$Subscriber;Ljava/util/concurrent/Flow$Subscriber;)V+243 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLTube$SSLTubeFlowDelegate.<init>(Ljdk/internal/net/http/common/SSLTube;Ljavax/net/ssl/SSLEngine;Ljava/util/concurrent/Executor;Ljava/util/function/Consumer;Ljdk/internal/net/http/common/SSLTube$SSLSubscriberWrapper;Ljdk/internal/net/http/common/FlowTube;)V+14 java.net.http@21.0.4
j  jdk.internal.net.http.common.SSLTube.<init>(Ljavax/net/ssl/SSLEngine;Ljava/util/concurrent/Executor;Ljava/util/function/Consumer;Ljdk/internal/net/http/common/FlowTube;)V+97 java.net.http@21.0.4
j  jdk.internal.net.http.AsyncSSLConnection.lambda$connectAsync$0(Ljava/lang/Void;)Ljava/lang/Void;+40 java.net.http@21.0.4
j  jdk.internal.net.http.AsyncSSLConnection$$Lambda+0x000000080016ed48.apply(Ljava/lang/Object;)Ljava/lang/Object;+8 java.net.http@21.0.4
j  java.util.concurrent.CompletableFuture$UniApply.tryFire(I)Ljava/util/concurrent/CompletableFuture;+106 java.base@21.0.4
j  java.util.concurrent.CompletableFuture.postComplete()V+76 java.base@21.0.4
j  java.util.concurrent.CompletableFuture$AsyncSupply.run()V+57 java.base@21.0.4
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@21.0.4
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@21.0.4
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.4
j  java.lang.Thread.run()V+19 java.base@21.0.4
v  ~StubRoutines::call_stub 0x00000175c31c100d
invokedynamic  186 invokedynamic  [0x00000175c31da520, 0x00000175c31da7f0]  720 bytes
[MachCode]
  0x00000175c31da520: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x00000175c31da540: 4424 0800 | 0000 00eb | 0150 4c89 | 6dc0 418b | 5501 f7d2 | 488b 4dd0 | 488b 4928 | c1e2 0448 
  0x00000175c31da560: 8d4c 1108 | 488b 1948 | 85db 0f85 | c100 0000 | bbba 0000 | 00e8 0500 | 0000 e999 | 0000 0048 
  0x00000175c31da580: 8bd3 488d | 4424 084c | 896d c049 | 8bcf c5f8 | 7749 89af | a803 0000 | 4989 8798 | 0300 0048 
  0x00000175c31da5a0: 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b850 | 5317 dafd | 7f00 00ff | d048 83c4 
  0x00000175c31da5c0: 08e9 0c00 | 0000 48b8 | 5053 17da | fd7f 0000 | ffd0 4883 | c420 49c7 | 8798 0300 | 0000 0000 
  0x00000175c31da5e0: 0049 c787 | a803 0000 | 0000 0000 | 49c7 87a0 | 0300 0000 | 0000 00c5 | f877 4983 | 7f08 000f 
  0x00000175c31da600: 8405 0000 | 00e9 f668 | feff 4c8b | 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 418b 5501 | f7d2 488b 
  0x00000175c31da620: 4dd0 488b | 4928 c1e2 | 0448 8d4c | 1108 488b | 190f b651 | 0ff6 c202 | 0f84 2400 | 0000 0fb7 
  0x00000175c31da640: 5108 488b | 45e8 488b | 4008 488b | 4008 488b | 4010 488b | 4010 488b | 008b 4490 | 1048 c1e0 
  0x00000175c31da660: 0350 0fb6 | 510e 49ba | f067 a6da | fd7f 0000 | 498b 14d2 | 524c 8b6d | d84d 85ed | 0f84 1200 
  0x00000175c31da680: 0000 4983 | 4508 0149 | 835d 0800 | 4983 c510 | 4c89 6dd8 | 488b 55d8 | 4885 d20f | 843d 0100 
  0x00000175c31da6a0: 0080 7af0 | 0a0f 8533 | 0100 0048 | 83c2 084c | 8b6a f841 | 83ed 0041 | 83fd 020f | 8c12 0100 
  0x00000175c31da6c0: 004c 8b6b | 0845 0fb7 | 6d2e 4c2b | 2a41 83ed | 014e 8b6c | ec08 4d85 | ed75 0ef6 | 4208 0175 
  0x00000175c31da6e0: 58f0 4883 | 4a08 01eb | 5045 8b6d | 0849 ba00 | 0000 0008 | 0000 004d | 03ea 4d8b | d54c 336a 
  0x00000175c31da700: 0849 f7c5 | fcff ffff | 742f 41f6 | c502 7529 | 4883 7a08 | 0074 1e48 | 837a 0801 | 7417 4d8b 
  0x00000175c31da720: ea4c 336a | 0849 f7c5 | fcff ffff | 740b 4883 | 4a08 02eb | 044c 896a | 0848 83c2 | 104c 8b6a 
  0x00000175c31da740: e841 83ed | 0241 83fd | 020f 8c84 | 0000 004c | 8b6b 0845 | 0fb7 6d2e | 4c2b 2a41 | 83ed 014e 
  0x00000175c31da760: 8b6c ec08 | 4d85 ed75 | 0ef6 4208 | 0175 58f0 | 4883 4a08 | 01eb 5045 | 8b6d 0849 | ba00 0000 
  0x00000175c31da780: 0008 0000 | 004d 03ea | 4d8b d54c | 336a 0849 | f7c5 fcff | ffff 742f | 41f6 c502 | 7529 4883 
  0x00000175c31da7a0: 7a08 0074 | 1e48 837a | 0801 7417 | 4d8b ea4c | 336a 0849 | f7c5 fcff | ffff 740b | 4883 4a08 
  0x00000175c31da7c0: 02eb 044c | 896a 0848 | 83c2 104c | 8b6a d841 | 83ed 0441 | c1e5 0349 | 03d5 4889 | 55d8 4c8d 
  0x00000175c31da7e0: 6c24 084c | 896d f0ff | 6368 660f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000175dd38f670, length=14, elements={
0x00000175b79d96e0, 0x00000175d862e7d0, 0x00000175d862fae0, 0x00000175d8677d70,
0x00000175d86787d0, 0x00000175d869f850, 0x00000175d8699be0, 0x00000175d869acd0,
0x00000175d8664fb0, 0x00000175d88283a0, 0x00000175d882ae30, 0x00000175dd375af0,
0x00000175dd446ba0, 0x00000175dd4c39c0
}

Java Threads: ( => current thread )
  0x00000175b79d96e0 JavaThread "main"                              [_thread_blocked, id=4048, stack(0x00000062bb500000,0x00000062bb600000) (1024K)]
  0x00000175d862e7d0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=47236, stack(0x00000062bbd00000,0x00000062bbe00000) (1024K)]
  0x00000175d862fae0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=44580, stack(0x00000062bbe00000,0x00000062bbf00000) (1024K)]
  0x00000175d8677d70 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=44836, stack(0x00000062bbf00000,0x00000062bc000000) (1024K)]
  0x00000175d86787d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=45808, stack(0x00000062bc000000,0x00000062bc100000) (1024K)]
  0x00000175d869f850 JavaThread "Service Thread"             daemon [_thread_blocked, id=18328, stack(0x00000062bc100000,0x00000062bc200000) (1024K)]
  0x00000175d8699be0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11192, stack(0x00000062bc200000,0x00000062bc300000) (1024K)]
  0x00000175d869acd0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=15572, stack(0x00000062bc300000,0x00000062bc400000) (1024K)]
  0x00000175d8664fb0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=46676, stack(0x00000062bc400000,0x00000062bc500000) (1024K)]
  0x00000175d88283a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22520, stack(0x00000062bc500000,0x00000062bc600000) (1024K)]
  0x00000175d882ae30 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=45484, stack(0x00000062bc600000,0x00000062bc700000) (1024K)]
  0x00000175dd375af0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=47896, stack(0x00000062bc700000,0x00000062bc800000) (1024K)]
=>0x00000175dd446ba0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_in_vm, id=16708, stack(0x00000062bc900000,0x00000062bca00000) (1024K)]
  0x00000175dd4c39c0 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=12216, stack(0x00000062bca00000,0x00000062bcb00000) (1024K)]
Total: 14

Other Threads:
  0x00000175d5a60760 VMThread "VM Thread"                           [id=4748, stack(0x00000062bbc00000,0x00000062bbd00000) (1024K)]
  0x00000175d85c09d0 WatcherThread "VM Periodic Task Thread"        [id=17172, stack(0x00000062bbb00000,0x00000062bbc00000) (1024K)]
  0x00000175d58d0980 WorkerThread "GC Thread#0"                     [id=13308, stack(0x00000062bb600000,0x00000062bb700000) (1024K)]
  0x00000175b7a50d50 ConcurrentGCThread "G1 Main Marker"            [id=47000, stack(0x00000062bb700000,0x00000062bb800000) (1024K)]
  0x00000175b7a518a0 WorkerThread "G1 Conc#0"                       [id=18104, stack(0x00000062bb800000,0x00000062bb900000) (1024K)]
  0x00000175d595c6c0 ConcurrentGCThread "G1 Refine#0"               [id=34480, stack(0x00000062bb900000,0x00000062bba00000) (1024K)]
  0x00000175d595cfd0 ConcurrentGCThread "G1 Service"                [id=23128, stack(0x00000062bba00000,0x00000062bbb00000) (1024K)]
Total: 7

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffddaa54748] Metaspace_lock - owner thread: 0x00000175dd446ba0

Heap address: 0x0000000603c00000, size: 8132 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192
 CPUs: 12 total, 12 available
 Memory: 32512M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8132M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 12288K [0x0000000603c00000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 0 survivors (0K)
 Metaspace       used 16375K, committed 16640K, reserved 1114112K
  class space    used 1601K, committed 1728K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000| PB 0x0000000603c00000| Untracked 
|   1|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000| PB 0x0000000604000000| Untracked 
|   2|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000| PB 0x0000000604400000| Untracked 
|   3|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000| PB 0x0000000604800000| Untracked 
|   4|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000| PB 0x0000000604c00000| Untracked 
|   5|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000| PB 0x0000000605000000| Untracked 
|   6|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000| PB 0x0000000605400000| Untracked 
|   7|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000| PB 0x0000000605800000| Untracked 
|   8|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000| PB 0x0000000605c00000| Untracked 
|   9|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000| PB 0x0000000606000000| Untracked 
|  10|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000| PB 0x0000000606400000| Untracked 
|  11|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000| PB 0x0000000606800000| Untracked 
|  12|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000| PB 0x0000000606c00000| Untracked 
|  13|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000| PB 0x0000000607000000| Untracked 
|  14|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000| PB 0x0000000607400000| Untracked 
|  15|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000| PB 0x0000000607800000| Untracked 
|  16|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000| PB 0x0000000607c00000| Untracked 
|  17|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000| PB 0x0000000608000000| Untracked 
|  18|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000| PB 0x0000000608400000| Untracked 
|  19|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000| PB 0x0000000608800000| Untracked 
|  20|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000| PB 0x0000000608c00000| Untracked 
|  21|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000| PB 0x0000000609000000| Untracked 
|  22|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000| PB 0x0000000609400000| Untracked 
|  23|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000| PB 0x0000000609800000| Untracked 
|  24|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000| PB 0x0000000609c00000| Untracked 
|  25|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000| PB 0x000000060a000000| Untracked 
|  26|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000| PB 0x000000060a400000| Untracked 
|  27|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000| PB 0x000000060a800000| Untracked 
|  28|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000| PB 0x000000060ac00000| Untracked 
|  29|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000| PB 0x000000060b000000| Untracked 
|  30|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000| PB 0x000000060b400000| Untracked 
|  31|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000| PB 0x000000060b800000| Untracked 
|  32|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000| PB 0x000000060bc00000| Untracked 
|  33|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000| PB 0x000000060c000000| Untracked 
|  34|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000| PB 0x000000060c400000| Untracked 
|  35|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000| PB 0x000000060c800000| Untracked 
|  36|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000| PB 0x000000060cc00000| Untracked 
|  37|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000| PB 0x000000060d000000| Untracked 
|  38|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000| PB 0x000000060d400000| Untracked 
|  39|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000| PB 0x000000060d800000| Untracked 
|  40|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000| PB 0x000000060dc00000| Untracked 
|  41|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000| PB 0x000000060e000000| Untracked 
|  42|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000| PB 0x000000060e400000| Untracked 
|  43|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000| PB 0x000000060e800000| Untracked 
|  44|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000| PB 0x000000060ec00000| Untracked 
|  45|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000| PB 0x000000060f000000| Untracked 
|  46|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000| PB 0x000000060f400000| Untracked 
|  47|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000| PB 0x000000060f800000| Untracked 
|  48|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000| PB 0x000000060fc00000| Untracked 
|  49|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000| PB 0x0000000610000000| Untracked 
|  50|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000| PB 0x0000000610400000| Untracked 
|  51|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000| PB 0x0000000610800000| Untracked 
|  52|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000| PB 0x0000000610c00000| Untracked 
|  53|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000| PB 0x0000000611000000| Untracked 
|  54|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000| PB 0x0000000611400000| Untracked 
|  55|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000| PB 0x0000000611800000| Untracked 
|  56|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000| PB 0x0000000611c00000| Untracked 
|  57|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000| PB 0x0000000612000000| Untracked 
|  58|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000| PB 0x0000000612400000| Untracked 
|  59|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000| PB 0x0000000612800000| Untracked 
|  60|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000| PB 0x0000000612c00000| Untracked 
|  61|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000| PB 0x0000000613000000| Untracked 
|  62|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000| PB 0x0000000613400000| Untracked 
|  63|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000| PB 0x0000000613800000| Untracked 
|  64|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000| PB 0x0000000613c00000| Untracked 
|  65|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000| PB 0x0000000614000000| Untracked 
|  66|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000| PB 0x0000000614400000| Untracked 
|  67|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000| PB 0x0000000614800000| Untracked 
|  68|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000| PB 0x0000000614c00000| Untracked 
|  69|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000| PB 0x0000000615000000| Untracked 
|  70|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000| PB 0x0000000615400000| Untracked 
|  71|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000| PB 0x0000000615800000| Untracked 
|  72|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000| PB 0x0000000615c00000| Untracked 
|  73|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000| PB 0x0000000616000000| Untracked 
|  74|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000| PB 0x0000000616400000| Untracked 
|  75|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000| PB 0x0000000616800000| Untracked 
|  76|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000| PB 0x0000000616c00000| Untracked 
|  77|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000| PB 0x0000000617000000| Untracked 
|  78|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000| PB 0x0000000617400000| Untracked 
|  79|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000| PB 0x0000000617800000| Untracked 
|  80|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000| PB 0x0000000617c00000| Untracked 
|  81|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000| PB 0x0000000618000000| Untracked 
|  82|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000| PB 0x0000000618400000| Untracked 
|  83|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000| PB 0x0000000618800000| Untracked 
|  84|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000| PB 0x0000000618c00000| Untracked 
|  85|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000| PB 0x0000000619000000| Untracked 
|  86|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000| PB 0x0000000619400000| Untracked 
|  87|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000| PB 0x0000000619800000| Untracked 
|  88|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000| PB 0x0000000619c00000| Untracked 
|  89|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000| PB 0x000000061a000000| Untracked 
|  90|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000| PB 0x000000061a400000| Untracked 
|  91|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000| PB 0x000000061a800000| Untracked 
|  92|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000| PB 0x000000061ac00000| Untracked 
|  93|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000| PB 0x000000061b000000| Untracked 
|  94|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000| PB 0x000000061b400000| Untracked 
|  95|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000| PB 0x000000061b800000| Untracked 
|  96|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000| PB 0x000000061bc00000| Untracked 
|  97|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000| PB 0x000000061c000000| Untracked 
|  98|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000| PB 0x000000061c400000| Untracked 
|  99|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000| PB 0x000000061c800000| Untracked 
| 100|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000| PB 0x000000061cc00000| Untracked 
| 101|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000| PB 0x000000061d000000| Untracked 
| 102|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000| PB 0x000000061d400000| Untracked 
| 103|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000| PB 0x000000061d800000| Untracked 
| 104|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000| PB 0x000000061dc00000| Untracked 
| 105|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000| PB 0x000000061e000000| Untracked 
| 106|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000| PB 0x000000061e400000| Untracked 
| 107|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000| PB 0x000000061e800000| Untracked 
| 108|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000| PB 0x000000061ec00000| Untracked 
| 109|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000| PB 0x000000061f000000| Untracked 
| 110|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000| PB 0x000000061f400000| Untracked 
| 111|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000| PB 0x000000061f800000| Untracked 
| 112|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000| PB 0x000000061fc00000| Untracked 
| 113|0x0000000620000000, 0x0000000620000000, 0x0000000620400000|  0%| F|  |TAMS 0x0000000620000000| PB 0x0000000620000000| Untracked 
| 114|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000| PB 0x0000000620400000| Untracked 
| 115|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000| PB 0x0000000620800000| Untracked 
| 116|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000| PB 0x0000000620c00000| Untracked 
| 117|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000| PB 0x0000000621000000| Untracked 
| 118|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000| PB 0x0000000621400000| Untracked 
| 119|0x0000000621800000, 0x0000000621800000, 0x0000000621c00000|  0%| F|  |TAMS 0x0000000621800000| PB 0x0000000621800000| Untracked 
| 120|0x0000000621c00000, 0x0000000621c00000, 0x0000000622000000|  0%| F|  |TAMS 0x0000000621c00000| PB 0x0000000621c00000| Untracked 
| 121|0x0000000622000000, 0x0000000622000000, 0x0000000622400000|  0%| F|  |TAMS 0x0000000622000000| PB 0x0000000622000000| Untracked 
| 122|0x0000000622400000, 0x0000000622400000, 0x0000000622800000|  0%| F|  |TAMS 0x0000000622400000| PB 0x0000000622400000| Untracked 
| 123|0x0000000622800000, 0x0000000622800000, 0x0000000622c00000|  0%| F|  |TAMS 0x0000000622800000| PB 0x0000000622800000| Untracked 
| 124|0x0000000622c00000, 0x0000000622e67900, 0x0000000623000000| 60%| E|  |TAMS 0x0000000622c00000| PB 0x0000000622c00000| Complete 
| 125|0x0000000623000000, 0x0000000623400000, 0x0000000623400000|100%| E|CS|TAMS 0x0000000623000000| PB 0x0000000623000000| Complete 
| 126|0x0000000623400000, 0x0000000623800000, 0x0000000623800000|100%| E|CS|TAMS 0x0000000623400000| PB 0x0000000623400000| Complete 
| 127|0x0000000623800000, 0x0000000623c00000, 0x0000000623c00000|100%| E|CS|TAMS 0x0000000623800000| PB 0x0000000623800000| Complete 

Card table byte_map: [0x00000175cc900000,0x00000175cd8f0000] _byte_map_base: 0x00000175c98e2000

Marking Bits: (CMBitMap*) 0x00000175b7a40660
 Bits: [0x00000175cd8f0000, 0x00000175d5800000)

Polling page: 0x00000175b6130000

Metaspace:

Usage:
  Non-class:     14.43 MB used.
      Class:      1.56 MB used.
       Both:     15.99 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      14.56 MB ( 23%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.69 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      16.25 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  1008.00 KB
       Class:  14.20 MB
        Both:  15.19 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 222.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 260.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 400.
num_chunk_merges: 0.
num_chunk_splits: 219.
num_chunks_enlarged: 87.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=315Kb max_used=315Kb free=119684Kb
 bounds [0x00000175c3760000, 0x00000175c39d0000, 0x00000175cac90000]
CodeHeap 'profiled nmethods': size=120000Kb used=1521Kb max_used=1521Kb free=118478Kb
 bounds [0x00000175bbc90000, 0x00000175bbf00000, 0x00000175c31c0000]
CodeHeap 'non-nmethods': size=5760Kb used=1414Kb max_used=1433Kb free=4345Kb
 bounds [0x00000175c31c0000, 0x00000175c3430000, 0x00000175c3760000]
 total_blobs=1489 nmethods=983 adapters=411
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.385 Thread 0x00000175d8664fb0 nmethod 971 0x00000175bbe08490 code [0x00000175bbe08660, 0x00000175bbe08928]
Event: 0.385 Thread 0x00000175d8664fb0  973       3       java.util.regex.Pattern$Start::match (90 bytes)
Event: 0.385 Thread 0x00000175d8664fb0 nmethod 973 0x00000175bbe08b10 code [0x00000175bbe08ce0, 0x00000175bbe090e8]
Event: 0.385 Thread 0x00000175d8664fb0  974       3       java.lang.String::regionMatches (150 bytes)
Event: 0.385 Thread 0x00000175d8664fb0 nmethod 974 0x00000175bbe09290 code [0x00000175bbe094e0, 0x00000175bbe09ce0]
Event: 0.385 Thread 0x00000175d8664fb0  972       3       java.util.regex.Matcher::find (69 bytes)
Event: 0.385 Thread 0x00000175d8664fb0 nmethod 972 0x00000175bbe09f90 code [0x00000175bbe0a140, 0x00000175bbe0a430]
Event: 0.385 Thread 0x00000175d8664fb0  975       3       java.util.TreeSet::contains (11 bytes)
Event: 0.385 Thread 0x00000175d8664fb0 nmethod 975 0x00000175bbe0a510 code [0x00000175bbe0a6c0, 0x00000175bbe0a8a0]
Event: 0.386 Thread 0x00000175d8664fb0  976       3       java.lang.StringLatin1::indexOf (121 bytes)
Event: 0.386 Thread 0x00000175d869acd0  977       4       java.util.regex.Pattern$SliceI::match (96 bytes)
Event: 0.386 Thread 0x00000175d8664fb0 nmethod 976 0x00000175bbe0a990 code [0x00000175bbe0ab60, 0x00000175bbe0b048]
Event: 0.386 Thread 0x00000175d8664fb0  980       3       java.util.KeyValueHolder::<init> (21 bytes)
Event: 0.386 Thread 0x00000175d8664fb0 nmethod 980 0x00000175bbe0b290 code [0x00000175bbe0b480, 0x00000175bbe0b968]
Event: 0.386 Thread 0x00000175d8664fb0  983       3       java.util.HashSet::contains (9 bytes)
Event: 0.386 Thread 0x00000175d8664fb0 nmethod 983 0x00000175bbe0bb10 code [0x00000175bbe0bcc0, 0x00000175bbe0bea0]
Event: 0.386 Thread 0x00000175d8664fb0  978       3       java.util.HashMap::containsKey (14 bytes)
Event: 0.386 Thread 0x00000175d8664fb0 nmethod 978 0x00000175bbe0bf90 code [0x00000175bbe0c140, 0x00000175bbe0c2d0]
Event: 0.386 Thread 0x00000175d8664fb0  979       3       java.util.ImmutableCollections$MapN$MapNIterator::nextIndex (56 bytes)
Event: 0.386 Thread 0x00000175d8664fb0 nmethod 979 0x00000175bbe0c390 code [0x00000175bbe0c520, 0x00000175bbe0c698]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.009 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
Event: 0.013 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll

Deoptimization events (12 events):
Event: 0.225 Thread 0x00000175b79d96e0 DEOPT PACKING pc=0x00000175bbcd2ca3 sp=0x00000062bb5fddb0
Event: 0.225 Thread 0x00000175b79d96e0 DEOPT UNPACKING pc=0x00000175c3214e42 sp=0x00000062bb5fd248 mode 0
Event: 0.229 Thread 0x00000175b79d96e0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000175c37773e8 relative=0x0000000000000588
Event: 0.229 Thread 0x00000175b79d96e0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000175c37773e8 method=java.lang.AbstractStringBuilder.inflateIfNeededFor(Ljava/lang/String;)V @ 14 c2
Event: 0.229 Thread 0x00000175b79d96e0 DEOPT PACKING pc=0x00000175c37773e8 sp=0x00000062bb5fdc90
Event: 0.229 Thread 0x00000175b79d96e0 DEOPT UNPACKING pc=0x00000175c32146a2 sp=0x00000062bb5fdb40 mode 2
Event: 0.277 Thread 0x00000175b79d96e0 DEOPT PACKING pc=0x00000175bbcaa0f8 sp=0x00000062bb5fe020
Event: 0.277 Thread 0x00000175b79d96e0 DEOPT UNPACKING pc=0x00000175c3214e42 sp=0x00000062bb5fd440 mode 0
Event: 0.358 Thread 0x00000175dd375af0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000175c3797d48 relative=0x00000000000002e8
Event: 0.358 Thread 0x00000175dd375af0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000175c3797d48 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.358 Thread 0x00000175dd375af0 DEOPT PACKING pc=0x00000175c3797d48 sp=0x00000062bc7ff300
Event: 0.358 Thread 0x00000175dd375af0 DEOPT UNPACKING pc=0x00000175c32146a2 sp=0x00000062bc7ff258 mode 2

Classes loaded (20 events):
Event: 0.387 Loading class sun/security/ssl/RSAKeyExchange$RSAKAGenerator
Event: 0.387 Loading class sun/security/ssl/RSAKeyExchange$RSAKAGenerator done
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange done
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange$DHEPossessionGenerator
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange$DHEPossessionGenerator done
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange$DHEKAGenerator
Event: 0.387 Loading class sun/security/ssl/DHKeyExchange$DHEKAGenerator done
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange done
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossessionGenerator
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEPossessionGenerator done
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHKAGenerator
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHKAGenerator done
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEKAGenerator
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEKAGenerator done
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEXDHKAGenerator
Event: 0.387 Loading class sun/security/ssl/ECDHKeyExchange$ECDHEXDHKAGenerator done
Event: 0.387 Loading class sun/security/ssl/XDHKeyExchange$XDHEPossession
Event: 0.387 Loading class sun/security/ssl/XDHKeyExchange$XDHEPossession done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.261 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062375bae0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000062375bae0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.262 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062375fe88}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x000000062375fe88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.262 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237678d0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x00000006237678d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.263 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062376e490}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x000000062376e490) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.263 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000623774f20}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000623774f20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.263 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237795a8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000006237795a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.267 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237a6388}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000006237a6388) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.268 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237b56a8}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237b56a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.270 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237e8cf8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000006237e8cf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.271 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237ef5c8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237ef5c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.271 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006237f29e8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006237f29e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.329 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006231e8228}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006231e8228) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.347 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233e5758}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006233e5758) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.347 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006233e90c0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000006233e90c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.351 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c17e28}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c17e28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.359 Thread 0x00000175b79d96e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622c99d08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622c99d08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.359 Thread 0x00000175dd375af0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006232124f0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000006232124f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.364 Thread 0x00000175dd375af0 Exception <a 'java/lang/NoSuchMethodError'{0x000000062324c1f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x000000062324c1f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.365 Thread 0x00000175dd446ba0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d1afe8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000622d1afe8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.369 Thread 0x00000175dd446ba0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000622d6cea0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000622d6cea0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (6 events):
Event: 0.094 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.094 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.102 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.218 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.218 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.012 Thread 0x00000175b79d96e0 Thread added: 0x00000175b79d96e0
Event: 0.054 Thread 0x00000175b79d96e0 Thread added: 0x00000175d862e7d0
Event: 0.054 Thread 0x00000175b79d96e0 Thread added: 0x00000175d862fae0
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d8677d70
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d86787d0
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d869f850
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d8699be0
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d869acd0
Event: 0.055 Thread 0x00000175b79d96e0 Thread added: 0x00000175d8664fb0
Event: 0.075 Thread 0x00000175b79d96e0 Thread added: 0x00000175d88283a0
Event: 0.078 Thread 0x00000175b79d96e0 Thread added: 0x00000175d882ae30
Event: 0.084 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
Event: 0.086 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
Event: 0.091 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
Event: 0.142 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
Event: 0.291 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
Event: 0.321 Loaded shared library D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll
Event: 0.325 Thread 0x00000175b79d96e0 Thread added: 0x00000175dd375af0
Event: 0.364 Thread 0x00000175dd375af0 Thread added: 0x00000175dd446ba0
Event: 0.373 Thread 0x00000175dd375af0 Thread added: 0x00000175dd4c39c0


Dynamic libraries:
0x00007ff6bb800000 - 0x00007ff6bb80a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.exe
0x00007ffe57a90000 - 0x00007ffe57ca7000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe57690000 - 0x00007ffe57754000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe551f0000 - 0x00007ffe555a7000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe54da0000 - 0x00007ffe54eb1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe4ff40000 - 0x00007ffe4ff58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jli.dll
0x00007ffe51630000 - 0x00007ffe5164b000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffe57490000 - 0x00007ffe5763f000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe55010000 - 0x00007ffe55036000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe55a60000 - 0x00007ffe55a89000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe54ef0000 - 0x00007ffe55008000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe555b0000 - 0x00007ffe5564a000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe29d80000 - 0x00007ffe2a013000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955\COMCTL32.dll
0x00007ffe56720000 - 0x00007ffe567c7000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe55a90000 - 0x00007ffe55ac1000 	C:\WINDOWS\System32\IMM32.DLL
0x000000005eca0000 - 0x000000005ecad000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffe563f0000 - 0x00007ffe564a2000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe56b70000 - 0x00007ffe56c18000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe54ec0000 - 0x00007ffe54ee8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe564b0000 - 0x00007ffe565c4000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe40350000 - 0x00007ffe40453000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffe55b70000 - 0x00007ffe563d9000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe566c0000 - 0x00007ffe5671e000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffe54730000 - 0x00007ffe5473a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe51950000 - 0x00007ffe5195c000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffe0b900000 - 0x00007ffe0b98d000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\msvcp140.dll
0x00007ffdd9d90000 - 0x00007ffddab47000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server\jvm.dll
0x00007ffe56c30000 - 0x00007ffe56ca1000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe54c70000 - 0x00007ffe54cbd000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe46f80000 - 0x00007ffe46fb4000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe54c50000 - 0x00007ffe54c63000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe53d60000 - 0x00007ffe53d78000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe50050000 - 0x00007ffe5005a000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\jimage.dll
0x00007ffe52810000 - 0x00007ffe52a42000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe567e0000 - 0x00007ffe56b6e000 	C:\WINDOWS\System32\combase.dll
0x00007ffe55980000 - 0x00007ffe55a57000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe3c460000 - 0x00007ffe3c492000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe55170000 - 0x00007ffe551eb000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe46f20000 - 0x00007ffe46f3f000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\java.dll
0x00007ffe46b40000 - 0x00007ffe46b58000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\zip.dll
0x00007ffe52c80000 - 0x00007ffe5357f000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe52b40000 - 0x00007ffe52c7f000 	C:\WINDOWS\SYSTEM32\wintypes.dll
0x00007ffe56cb0000 - 0x00007ffe56da9000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe54cd0000 - 0x00007ffe54cf7000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe50010000 - 0x00007ffe50020000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\net.dll
0x00007ffe4f4c0000 - 0x00007ffe4f5f6000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe541d0000 - 0x00007ffe54239000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe46ad0000 - 0x00007ffe46ae6000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\nio.dll
0x00007ffe54430000 - 0x00007ffe5444b000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffe53cc0000 - 0x00007ffe53cf5000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffe542c0000 - 0x00007ffe542e8000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffe54420000 - 0x00007ffe5442c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffe537e0000 - 0x00007ffe5380d000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffe567d0000 - 0x00007ffe567d9000 	C:\WINDOWS\System32\NSI.dll
0x00007ffe46b30000 - 0x00007ffe46b3e000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\sunmscapi.dll
0x00007ffe55650000 - 0x00007ffe557b6000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffe546f0000 - 0x00007ffe5471d000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffe546b0000 - 0x00007ffe546e7000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffdbb840000 - 0x00007ffdbb848000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffe46aa0000 - 0x00007ffe46aa9000 	D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.3672_none_2713b9d173822955;C:\Program Files (x86)\360\360Safe\safemon;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://e.coding.net': 
java_class_path (initial): D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/plugins/vcs-git/lib/git4idea-rt.jar;D:/Program Files/JetBrains/IntelliJ IDEA 2024.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8527020032                                {product} {ergonomic}
   size_t MaxNewSize                               = 5112856576                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8527020032                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\software\jdk-17.0.2
CLASSPATH=.;C:\Program Files\Java\jdk1.8.0_202\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_202\lib\tools.jar;
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;D:\Program Files\ImageMagick;D:\software\jdk-21_windows-x64_bin\jdk-21.0.3\bin;C:\Program Files\Java\jdk1.8.0_202\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\Program Files\Python311\Scripts;D:\Program Files\Python311;C:\Python310\Scripts;C:\Python310;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Git\cmd;C:\ProgramData\chocolatey\bin;D:\software\Microsoft VS Code\bin;C:\Program Files\OpenVPN\bin;D:\software\apache-maven-3.6.1\bin;C:\Program Files (x86)\Tencent\΢��web�����߹���\dll;%NVM_H;ME%;C:\Program Files\nodejs;C:\Program Files\dotnet;D:\Program Files\MongoDB\Server\7.0\bin;D:\software\protoc-25.0-rc-1-win64\bin;C:\Program Files\Tesseract-OCR;D:\software\ffmpeg-master-latest-win64-gpl\bin;D:\Program Files\Go\bin;C:\TDengine;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;E:\sofware\BtSoft\php\74;C:\ProgramData\ComposerSetup\bin;E:\Program Files (x86)\Tencent\΢��web������;C:\Program Files\python;C:\Program Files\python\Scripts;E:\sofware\BtSoft\panel\script;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;D:\Program Files\JetBrains\PyCharm 2022.3.2\bin;C:\Users\<USER>\AppData\Local\Programs\Azure Dev CLI;D:\Program Files\JetBrains\WebStorm 2023.2.6\bin;C:\Users\<USER>\go\bin;D:\Program Files\JetBrains\IntelliJ IDEA 2024.2.3\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Links
USERNAME=EDY
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 20, weak refs: 0

JNI global refs memory usage: 835, weak refs: 201

Process memory usage:
Resident Set Size: 75488K (0% of 33293192K total physical memory with 5213412K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 14906K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1453K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.3958)
OS uptime: 27 days 1:11 hours

CPU: total 12 (initial active 12) (6 cores per cpu, 2 threads per core) family 6 model 165 stepping 3 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for all 12 processors :
  Max Mhz: 3101, Current Mhz: 3101, Mhz Limit: 3101

Memory: 4k page, system-wide physical 32512M (5091M free)
TotalPageFile size 42752M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 73M, peak: 73M
current process commit charge ("private bytes"): 618M, peak: 619M

vm_info: OpenJDK 64-Bit Server VM (21.0.4+13-b509.17) for windows-amd64 JRE (21.0.4+13-b509.17), built on 2024-09-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
